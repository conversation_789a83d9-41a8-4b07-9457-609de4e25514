# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Logs
*.log
logs/

# Documentation
README.md
*.md

# Test files
test_*
*_test.py

# Unused app files
app.py
app_simple.py

# Node.js (if any)
node_modules/
npm-debug.log*

# Temporary files
*.tmp
*.temp
