# Solana NFT Creator Environment Configuration

# Solana Network Configuration
SOLANA_RPC_URL=https://api.devnet.solana.com
# For mainnet: https://api.mainnet-beta.solana.com
# For testnet: https://api.testnet.solana.com

# Wallet Configuration
SOLANA_WALLET_FILE_PATH=/app/app/wallet.json

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=true

# API Configuration
API_PORT=5000
API_HOST=0.0.0.0

# Logging
LOG_LEVEL=INFO
PYTHONUNBUFFERED=1

# Security (for production)
# SECRET_KEY=your-secret-key-here
# CORS_ORIGINS=https://yourdomain.com
