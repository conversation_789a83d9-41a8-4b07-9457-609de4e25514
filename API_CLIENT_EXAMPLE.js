// Solana NFT Creator & Marketplace API Client
// Base URL: http://localhost:5002

class SolanaNFTAPI {
  constructor(baseURL = 'http://localhost:5002') {
    this.baseURL = baseURL;
  }

  // Helper method for API calls
  async apiCall(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }
      
      return data;
    } catch (error) {
      console.error(`API call failed: ${endpoint}`, error);
      throw error;
    }
  }

  // ===== HEALTH & STATUS =====
  async getHealth() {
    return this.apiCall('/health');
  }

  async getStatus() {
    return this.apiCall('/status');
  }

  // ===== WALLET =====
  async getWalletBalance() {
    return this.apiCall('/api/v1/wallet/balance');
  }

  async getWalletInfo() {
    return this.apiCall('/api/v1/wallet/info');
  }

  // ===== NFT CREATION =====
  async createNFT(nftData) {
    return this.apiCall('/api/v1/create_nft', {
      method: 'POST',
      body: JSON.stringify(nftData)
    });
  }

  async createSimpleNFT(nftData) {
    return this.apiCall('/api/v1/create_nft_simple', {
      method: 'POST',
      body: JSON.stringify(nftData)
    });
  }

  async createToken(tokenData) {
    return this.apiCall('/api/v1/create_token', {
      method: 'POST',
      body: JSON.stringify(tokenData)
    });
  }

  // ===== NFT INFORMATION =====
  async getNFTInfo(mintAddress) {
    return this.apiCall(`/api/v1/nft/${mintAddress}`);
  }

  async getAllNFTs() {
    return this.apiCall('/api/v1/nfts');
  }

  async getNFTExamples() {
    return this.apiCall('/api/v1/nft_examples');
  }

  // ===== AUCTION HOUSE =====
  async createAuctionHouse(config = {}) {
    return this.apiCall('/api/v1/marketplace/auction_house/create', {
      method: 'POST',
      body: JSON.stringify(config)
    });
  }

  async getAuctionHouseInfo() {
    return this.apiCall('/api/v1/marketplace/auction_house/info');
  }

  // ===== MARKETPLACE OPERATIONS =====
  async listNFT(listingData) {
    return this.apiCall('/api/v1/marketplace/list', {
      method: 'POST',
      body: JSON.stringify(listingData)
    });
  }

  async placeBid(bidData) {
    return this.apiCall('/api/v1/marketplace/bid', {
      method: 'POST',
      body: JSON.stringify(bidData)
    });
  }

  async purchaseNFT(purchaseData) {
    return this.apiCall('/api/v1/marketplace/purchase', {
      method: 'POST',
      body: JSON.stringify(purchaseData)
    });
  }

  // ===== MANAGEMENT =====
  async acceptBid(bidId) {
    return this.apiCall(`/api/v1/marketplace/bid/${bidId}/accept`, {
      method: 'POST'
    });
  }

  async cancelListing(listingId) {
    return this.apiCall(`/api/v1/marketplace/listing/${listingId}/cancel`, {
      method: 'POST'
    });
  }

  async cancelBid(bidId) {
    return this.apiCall(`/api/v1/marketplace/bid/${bidId}/cancel`, {
      method: 'POST'
    });
  }

  // ===== DATA RETRIEVAL =====
  async getListings(nftMint = null) {
    const endpoint = nftMint 
      ? `/api/v1/marketplace/listings?nft_mint=${nftMint}`
      : '/api/v1/marketplace/listings';
    return this.apiCall(endpoint);
  }

  async getBids(nftMint = null) {
    const endpoint = nftMint 
      ? `/api/v1/marketplace/bids?nft_mint=${nftMint}`
      : '/api/v1/marketplace/bids';
    return this.apiCall(endpoint);
  }

  async getSales() {
    return this.apiCall('/api/v1/marketplace/sales');
  }

  async getMarketplaceStats() {
    return this.apiCall('/api/v1/marketplace/stats');
  }

  async getMarketplaceExamples() {
    return this.apiCall('/api/v1/marketplace/examples');
  }
}

// ===== USAGE EXAMPLES =====

// Initialize API client
const api = new SolanaNFTAPI();

// Example: Create NFT
async function createMyNFT() {
  try {
    const nftData = {
      name: "My Awesome NFT",
      symbol: "AWESOME",
      description: "This is my first NFT created with the API",
      image_url: "https://example.com/my-nft-image.png",
      attributes: [
        { trait_type: "Color", value: "Blue" },
        { trait_type: "Rarity", value: "Rare" }
      ],
      seller_fee_basis_points: 500 // 5% royalty
    };

    const result = await api.createNFT(nftData);
    console.log('NFT created:', result);
    return result;
  } catch (error) {
    console.error('Failed to create NFT:', error);
  }
}

// Example: List NFT for sale
async function listNFTForSale(nftMintAddress, price) {
  try {
    const listingData = {
      nft_mint: nftMintAddress,
      price: price, // Price in SOL
      duration_hours: 24 // List for 24 hours
    };

    const result = await api.listNFT(listingData);
    console.log('NFT listed:', result);
    return result;
  } catch (error) {
    console.error('Failed to list NFT:', error);
  }
}

// Example: Get marketplace data
async function getMarketplaceData() {
  try {
    const [listings, bids, stats] = await Promise.all([
      api.getListings(),
      api.getBids(),
      api.getMarketplaceStats()
    ]);

    console.log('Marketplace Data:', {
      listings: listings.listings,
      bids: bids.bids,
      stats: stats.stats
    });

    return { listings, bids, stats };
  } catch (error) {
    console.error('Failed to get marketplace data:', error);
  }
}

// Example: Place bid on NFT
async function placeBidOnNFT(nftMintAddress, bidPrice) {
  try {
    const bidData = {
      nft_mint: nftMintAddress,
      price: bidPrice, // Bid price in SOL
      duration_hours: 12 // Bid valid for 12 hours
    };

    const result = await api.placeBid(bidData);
    console.log('Bid placed:', result);
    return result;
  } catch (error) {
    console.error('Failed to place bid:', error);
  }
}

// Example: Purchase NFT directly
async function purchaseNFT(nftMintAddress, maxPrice) {
  try {
    const purchaseData = {
      nft_mint: nftMintAddress,
      max_price: maxPrice // Maximum price willing to pay
    };

    const result = await api.purchaseNFT(purchaseData);
    console.log('NFT purchased:', result);
    return result;
  } catch (error) {
    console.error('Failed to purchase NFT:', error);
  }
}

// Example: Setup marketplace
async function setupMarketplace() {
  try {
    // Create auction house
    const auctionHouse = await api.createAuctionHouse({
      seller_fee_basis_points: 250, // 2.5% fee
      requires_sign_off: false,
      can_change_sale_price: true
    });

    console.log('Auction house created:', auctionHouse);
    return auctionHouse;
  } catch (error) {
    console.error('Failed to setup marketplace:', error);
  }
}

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SolanaNFTAPI;
}

// Example usage in browser
// const api = new SolanaNFTAPI();
// api.getHealth().then(console.log);
// api.getMarketplaceStats().then(console.log);
