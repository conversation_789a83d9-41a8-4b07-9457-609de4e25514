{"project_info": {"name": "Solana NFT Creator & Marketplace", "description": "Complete NFT Marketplace Platform with Metaplex Auction House integration", "base_url": "http://localhost:5002", "version": "1.0.0", "features": ["SPL Token Creation", "NFT Minting with Metadata", "Metaplex Auction House Integration", "NFT Marketplace (List, Bid, Purchase)", "Wallet Management", "Transaction Tracking"]}, "endpoints": {"health_status": {"health_check": {"method": "GET", "endpoint": "/health", "description": "Basic health check", "response": {"status": "healthy", "message": "API is running", "version": "1.0.0"}}, "detailed_status": {"method": "GET", "endpoint": "/status", "description": "Detailed system status including wallet and network info", "response": {"status": "healthy", "network": "devnet", "wallet_address": "Da4pRG8NLaXMFTEA86gtHgpkU4RR3kVH27SWKeCVHBEU", "balance_sol": 0.001}}}, "token_creation": {"create_spl_token": {"method": "POST", "endpoint": "/api/v1/create_token", "description": "Create SPL Token (fungible or NFT-like)", "request_body": {"name": "My Token", "symbol": "MTK", "amount": 1000, "description": "My awesome token"}, "response": {"success": true, "message": "<PERSON><PERSON> created successfully!", "token_mint_address": "ABC123...", "associated_token_account": "DEF456...", "transaction_signature": "GHI789...", "explorer_url": "https://explorer.solana.com/tx/...", "token_details": {"name": "My Token", "symbol": "MTK", "amount_minted": 1000, "decimals": 0}}}}, "nft_creation": {"create_nft_full": {"method": "POST", "endpoint": "/api/v1/create_nft", "description": "Create NFT with full metadata (Metaplex compatible)", "request_body": {"name": "My NFT", "symbol": "MNFT", "description": "My first NFT", "image_url": "https://example.com/image.png", "external_url": "https://myproject.com", "animation_url": "https://example.com/animation.mp4", "attributes": [{"trait_type": "Color", "value": "Blue"}, {"trait_type": "<PERSON><PERSON>", "value": "Rare"}, {"trait_type": "Level", "value": "10", "display_type": "number"}], "collection_name": "My Collection", "collection_family": "My NFT Series", "seller_fee_basis_points": 500, "is_mutable": true, "max_supply": 1000}, "response": {"success": true, "message": "NFT created successfully!", "nft_mint_address": "ABC123...", "metadata_uri": "file://path/to/metadata.json", "associated_token_account": "DEF456...", "transaction_signature": "GHI789...", "explorer_url": "https://explorer.solana.com/tx/...", "nft_details": {"name": "My NFT", "symbol": "MNFT", "description": "My first NFT", "image": "https://example.com/image.png", "is_mutable": true, "type": "simple_nft"}, "created_at": "2025-06-29T19:45:14.678632"}}, "create_nft_simple": {"method": "POST", "endpoint": "/api/v1/create_nft_simple", "description": "Create simple NFT-like token (minimal fields)", "request_body": {"name": "Simple NFT", "symbol": "SIMPLE"}, "response": {"success": true, "message": "NFT-like token created successfully!", "token_mint_address": "ABC123...", "nft_like": true, "metadata_note": "This is a simple NFT-like token"}}}, "nft_information": {"get_nft_info": {"method": "GET", "endpoint": "/api/v1/nft/{mint_address}", "description": "Get NFT information and metadata", "response": {"mint_address": "ABC123...", "type": "simple_nft", "metadata_file": "metadata/simple_nft_12345678.json", "metadata": {"name": "My NFT", "symbol": "MNFT", "description": "My first NFT", "image": "https://example.com/image.png", "attributes": []}}}, "list_all_nfts": {"method": "GET", "endpoint": "/api/v1/nfts", "description": "List all created NFTs", "response": {"nfts": [{"metadata_file": "metadata/simple_nft_12345678.json", "metadata": {}, "created_at": 1640995200}], "count": 1, "metadata_directory": "metadata"}}, "nft_examples": {"method": "GET", "endpoint": "/api/v1/nft_examples", "description": "Get NFT creation examples and API usage"}}, "wallet_management": {"get_balance": {"method": "GET", "endpoint": "/api/v1/wallet/balance", "description": "Get wallet SOL balance", "response": {"wallet_address": "Da4pRG8NLaXMFTEA86gtHgpkU4RR3kVH27SWKeCVHBEU", "balance_sol": 0.001, "balance_lamports": 1000000}}, "get_wallet_info": {"method": "GET", "endpoint": "/api/v1/wallet/info", "description": "Get wallet information", "response": {"wallet_address": "Da4pRG8NLaXMFTEA86gtHgpkU4RR3kVH27SWKeCVHBEU", "network": "devnet", "balance_sol": 0.001}}}, "marketplace": {"auction_house_management": {"create_auction_house": {"method": "POST", "endpoint": "/api/v1/marketplace/auction_house/create", "description": "Create new Auction House for marketplace", "request_body": {"seller_fee_basis_points": 250, "requires_sign_off": false, "can_change_sale_price": true}, "response": {"success": true, "message": "Auction House created successfully!", "auction_house_address": "reyXeAC9S1tLgNMMVCY5HYGtoSRFusNyokdeEfNeNxJ", "data": {"authority": "Da4pRG8NLaXMFTEA86gtHgpkU4RR3kVH27SWKeCVHBEU", "treasury_mint": "So11111111111111111111111111111111111111112", "seller_fee_basis_points": 250, "requires_sign_off": false, "can_change_sale_price": true}}}, "get_auction_house_info": {"method": "GET", "endpoint": "/api/v1/marketplace/auction_house/info", "description": "Get Auction House configuration", "response": {"success": true, "auction_house": {"authority": "Da4pRG8NLaXMFTEA86gtHgpkU4RR3kVH27SWKeCVHBEU", "treasury_mint": "So11111111111111111111111111111111111111112", "seller_fee_basis_points": 250}}}}, "nft_listing": {"list_nft": {"method": "POST", "endpoint": "/api/v1/marketplace/list", "description": "List NFT for sale in marketplace", "request_body": {"nft_mint": "********************************************", "price": 1.5, "duration_hours": 24, "currency_mint": "So11111111111111111111111111111111111111112"}, "response": {"success": true, "message": "NFT listed successfully!", "data": {"listing_id": "83a01c6e-2aa8-4c05-9a69-192f613ee2d1", "nft_mint": "********************************************", "seller": "Da4pRG8NLaXMFTEA86gtHgpkU4RR3kVH27SWKeCVHBEU", "price": 1.5, "status": "active", "expires_at": "2025-06-30T19:49:17.077127", "created_at": "2025-06-29T19:49:17.077200"}}}, "cancel_listing": {"method": "POST", "endpoint": "/api/v1/marketplace/listing/{listing_id}/cancel", "description": "Cancel NFT listing (seller only)", "response": {"success": true, "message": "Listing cancelled successfully!", "data": {"listing_id": "83a01c6e-2aa8-4c05-9a69-192f613ee2d1", "status": "cancelled"}}}}, "bidding_system": {"place_bid": {"method": "POST", "endpoint": "/api/v1/marketplace/bid", "description": "Place bid on NFT", "request_body": {"nft_mint": "********************************************", "price": 1.2, "duration_hours": 12, "currency_mint": "So11111111111111111111111111111111111111112"}, "response": {"success": true, "message": "Bid placed successfully!", "data": {"bid_id": "abc123-def456-ghi789", "nft_mint": "********************************************", "bidder": "Da4pRG8NLaXMFTEA86gtHgpkU4RR3kVH27SWKeCVHBEU", "price": 1.2, "status": "active", "expires_at": "2025-06-30T07:46:38.833365"}}}, "accept_bid": {"method": "POST", "endpoint": "/api/v1/marketplace/bid/{bid_id}/accept", "description": "Accept bid (NFT owner only)", "response": {"success": true, "message": "<PERSON><PERSON> accepted successfully!", "transaction_signature": "demo_accept_bid_12345678", "data": {"sale_id": "sale123-456-789", "nft_mint": "********************************************", "seller": "Da4pRG8NLaXMFTEA86gtHgpkU4RR3kVH27SWKeCVHBEU", "buyer": "Buyer_Address", "price": 1.2, "status": "completed"}}}, "cancel_bid": {"method": "POST", "endpoint": "/api/v1/marketplace/bid/{bid_id}/cancel", "description": "Cancel bid (bidder only)", "response": {"success": true, "message": "Bid cancelled successfully!"}}}, "direct_purchase": {"purchase_nft": {"method": "POST", "endpoint": "/api/v1/marketplace/purchase", "description": "Purchase NFT directly (buy now)", "request_body": {"nft_mint": "********************************************", "max_price": 2.0, "currency_mint": "So11111111111111111111111111111111111111112"}, "response": {"success": true, "message": "NFT purchased successfully!", "transaction_signature": "demo_purchase_12345678", "data": {"sale_id": "purchase123-456-789", "nft_mint": "********************************************", "seller": "Seller_Address", "buyer": "Da4pRG8NLaXMFTEA86gtHgpkU4RR3kVH27SWKeCVHBEU", "price": 1.5, "status": "completed", "completed_at": "2025-06-29T19:47:11.401934"}}}}, "marketplace_data": {"get_listings": {"method": "GET", "endpoint": "/api/v1/marketplace/listings", "description": "Get all active NFT listings", "query_params": {"nft_mint": "optional - filter by specific NFT"}, "response": {"success": true, "listings": [{"listing_id": "83a01c6e-2aa8-4c05-9a69-192f613ee2d1", "nft_mint": "********************************************", "seller": "Da4pRG8NLaXMFTEA86gtHgpkU4RR3kVH27SWKeCVHBEU", "price": 0.5, "currency_mint": "So11111111111111111111111111111111111111112", "status": "active", "created_at": "2025-06-29T19:49:17.077200", "expires_at": "2025-06-30T19:49:17.077127"}], "count": 1}}, "get_bids": {"method": "GET", "endpoint": "/api/v1/marketplace/bids", "description": "Get all active bids", "query_params": {"nft_mint": "optional - filter by specific NFT"}, "response": {"success": true, "bids": [{"bid_id": "abc123-def456-ghi789", "nft_mint": "********************************************", "bidder": "Da4pRG8NLaXMFTEA86gtHgpkU4RR3kVH27SWKeCVHBEU", "price": 1.2, "status": "active", "created_at": "2025-06-29T19:46:38.833365", "expires_at": "2025-06-30T07:46:38.833365"}], "count": 1}}, "get_sales": {"method": "GET", "endpoint": "/api/v1/marketplace/sales", "description": "Get all completed sales", "response": {"success": true, "sales": [{"sale_id": "sale123-456-789", "nft_mint": "********************************************", "seller": "Seller_Address", "buyer": "Buyer_Address", "price": 1.5, "status": "completed", "transaction_signature": "demo_purchase_12345678", "completed_at": "2025-06-29T19:47:11.401934"}], "count": 1}}, "get_marketplace_stats": {"method": "GET", "endpoint": "/api/v1/marketplace/stats", "description": "Get marketplace statistics", "response": {"success": true, "stats": {"active_listings": 1, "active_bids": 0, "total_sales": 0, "total_volume_sol": 0, "average_price_sol": 0, "auction_house_configured": true}}}, "get_examples": {"method": "GET", "endpoint": "/api/v1/marketplace/examples", "description": "Get API usage examples for marketplace"}}}}}