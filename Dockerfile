# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application
COPY main.py .
COPY src/ ./src/

# Create app directory for wallet
RUN mkdir -p /app/app

EXPOSE 5001

# Set environment variables
ENV SOLANA_RPC_URL="https://api.devnet.solana.com"
ENV SOLANA_WALLET_FILE_PATH="/app/app/wallet.json"
ENV FLASK_APP=main.py
ENV PYTHONUNBUFFERED=1

# Healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5001/health || exit 1

CMD ["python", "main.py"]

