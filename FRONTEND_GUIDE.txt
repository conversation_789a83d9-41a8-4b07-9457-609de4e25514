SOLANA NFT CREATOR & MARKETPLACE - FRONTEND INTEGRATION GUIDE
================================================================

BASE URL: http://localhost:5002

PROJECT OVERVIEW:
- Complete NFT Marketplace Platform on Solana
- SPL Token & NFT Creation
- Metaplex Auction House Integration
- Full Marketplace: List, Bid, Purchase NFTs

FILES PROVIDED:
1. API_DOCUMENTATION.json - Complete API documentation with examples
2. FRONTEND_INTERFACES.ts - TypeScript interfaces for all data types
3. FRONTEND_GUIDE.txt - This integration guide

===== QUICK START =====

1. HEALTH CHECK
   GET /health
   - Check if API is running
   - Returns: { status: "healthy", message: "API is running", version: "1.0.0" }

2. WALLET INFO
   GET /api/v1/wallet/balance
   - Get current wallet SOL balance
   - Returns: { wallet_address: "...", balance_sol: 0.001, balance_lamports: 1000000 }

===== CORE FEATURES =====

A. NFT CREATION
   POST /api/v1/create_nft
   Body: {
     "name": "My NFT",
     "symbol": "MNFT", 
     "description": "My first NFT",
     "image_url": "https://example.com/image.png",
     "attributes": [{"trait_type": "Color", "value": "Blue"}]
   }

B. MARKETPLACE SETUP
   POST /api/v1/marketplace/auction_house/create
   Body: {
     "seller_fee_basis_points": 250,
     "requires_sign_off": false,
     "can_change_sale_price": true
   }

C. LIST NFT FOR SALE
   POST /api/v1/marketplace/list
   Body: {
     "nft_mint": "NFT_MINT_ADDRESS",
     "price": 1.5,
     "duration_hours": 24
   }

D. PLACE BID
   POST /api/v1/marketplace/bid
   Body: {
     "nft_mint": "NFT_MINT_ADDRESS",
     "price": 1.2,
     "duration_hours": 12
   }

E. PURCHASE NFT
   POST /api/v1/marketplace/purchase
   Body: {
     "nft_mint": "NFT_MINT_ADDRESS",
     "max_price": 2.0
   }

===== DATA ENDPOINTS =====

GET /api/v1/marketplace/listings - Get all active listings
GET /api/v1/marketplace/bids - Get all active bids  
GET /api/v1/marketplace/sales - Get completed sales
GET /api/v1/marketplace/stats - Get marketplace statistics
GET /api/v1/nfts - Get all created NFTs

===== FRONTEND PAGES SUGGESTED =====

1. DASHBOARD
   - Show marketplace stats (/marketplace/stats)
   - Recent sales, active listings count
   - Total volume, average price

2. CREATE NFT
   - Form for NFT creation (/create_nft)
   - Image upload, metadata fields
   - Attributes, collection info

3. MY NFTS
   - List user's NFTs (/nfts)
   - Show NFT cards with images
   - Options to list for sale

4. MARKETPLACE
   - Browse all listings (/marketplace/listings)
   - Filter by price, attributes
   - Buy now buttons

5. NFT DETAIL PAGE
   - Show NFT metadata, image
   - Current listing info
   - Place bid / Buy now buttons
   - Bid history

6. MY LISTINGS
   - User's active listings
   - Cancel listing option
   - View bids received

7. MY BIDS
   - User's active bids
   - Cancel bid option
   - Bid status tracking

===== IMPORTANT NOTES =====

RESPONSE FORMAT:
All API responses follow this pattern:
{
  "success": true/false,
  "message": "Description",
  "data": { ... actual data ... },
  "transaction_signature": "...", // for blockchain operations
  "explorer_url": "https://explorer.solana.com/tx/..." // for viewing on Solana Explorer
}

ERROR HANDLING:
- Check "success" field in all responses
- Display "message" field to users
- Handle network errors gracefully

WALLET INTEGRATION:
- Current API uses server-side wallet
- For production: integrate with Phantom/Solflare wallet
- User needs to connect wallet for transactions

REAL-TIME UPDATES:
- Poll marketplace data every 30-60 seconds
- Refresh listings/bids after user actions
- Show loading states during transactions

PRICE DISPLAY:
- All prices are in SOL (Solana native token)
- 1 SOL = 1,000,000,000 lamports
- Display prices with 2-4 decimal places

STATUS TRACKING:
- Listings: active, sold, cancelled, expired
- Bids: active, accepted, rejected, cancelled, expired  
- Sales: pending, completed, failed, cancelled

===== EXAMPLE API CALLS =====

// Create NFT
const createNFT = async (nftData) => {
  const response = await fetch('http://localhost:5002/api/v1/create_nft', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(nftData)
  });
  return await response.json();
};

// Get marketplace listings
const getListings = async () => {
  const response = await fetch('http://localhost:5002/api/v1/marketplace/listings');
  return await response.json();
};

// List NFT for sale
const listNFT = async (listingData) => {
  const response = await fetch('http://localhost:5002/api/v1/marketplace/list', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(listingData)
  });
  return await response.json();
};

===== TESTING DATA =====

EXISTING NFT MINT ADDRESS (for testing):
********************************************

AUCTION HOUSE ADDRESS:
reyXeAC9S1tLgNMMVCY5HYGtoSRFusNyokdeEfNeNxJ

WALLET ADDRESS:
Da4pRG8NLaXMFTEA86gtHgpkU4RR3kVH27SWKeCVHBEU

===== NEXT STEPS =====

1. Review API_DOCUMENTATION.json for complete endpoint details
2. Use FRONTEND_INTERFACES.ts for TypeScript type safety
3. Start with health check and wallet info endpoints
4. Implement NFT creation form
5. Build marketplace listing display
6. Add bidding and purchase functionality
7. Implement real-time data updates

For questions or issues, check the API response messages and console logs.
The API includes comprehensive error handling and validation.
