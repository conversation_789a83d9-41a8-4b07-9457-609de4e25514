// TypeScript Interfaces for Solana NFT Creator & Marketplace
// Base URL: http://localhost:5002

// ===== COMMON TYPES =====
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface TransactionResponse {
  success: boolean;
  message: string;
  transaction_signature?: string;
  explorer_url?: string;
  created_at?: string;
}

// ===== NFT TYPES =====
export interface NFTAttribute {
  trait_type: string;
  value: string;
  display_type?: string;
}

export interface NFTFile {
  uri: string;
  type: string;
  cdn?: boolean;
}

export interface NFTCreator {
  address: string;
  share: number;
  verified: boolean;
}

export interface NFTCollection {
  name: string;
  family?: string;
}

export interface NFTProperties {
  files: NFTFile[];
  category: string;
  creators: NFTCreator[];
}

export interface NFTMetadata {
  name: string;
  symbol: string;
  description: string;
  image: string;
  external_url?: string;
  animation_url?: string;
  attributes: NFTAttribute[];
  properties: NFTProperties;
  collection?: NFTCollection;
  seller_fee_basis_points: number;
}

// ===== NFT CREATION =====
export interface CreateNFTRequest {
  name: string;
  symbol: string;
  description: string;
  image_url: string;
  external_url?: string;
  animation_url?: string;
  attributes?: NFTAttribute[];
  collection_name?: string;
  collection_family?: string;
  seller_fee_basis_points?: number;
  is_mutable?: boolean;
  max_supply?: number;
}

export interface CreateNFTResponse extends TransactionResponse {
  nft_mint_address?: string;
  metadata_uri?: string;
  associated_token_account?: string;
  nft_details?: {
    name: string;
    symbol: string;
    description: string;
    image: string;
    is_mutable: boolean;
    type: string;
  };
}

export interface CreateTokenRequest {
  name: string;
  symbol: string;
  amount: number;
  description?: string;
}

export interface CreateTokenResponse extends TransactionResponse {
  token_mint_address?: string;
  associated_token_account?: string;
  token_details?: {
    name: string;
    symbol: string;
    amount_minted: number;
    decimals: number;
  };
}

// ===== MARKETPLACE TYPES =====
export interface AuctionHouseConfig {
  authority: string;
  treasury_mint: string;
  fee_withdrawal_destination: string;
  treasury_withdrawal_destination: string;
  seller_fee_basis_points: number;
  requires_sign_off: boolean;
  can_change_sale_price: boolean;
}

export interface CreateAuctionHouseRequest {
  seller_fee_basis_points?: number;
  requires_sign_off?: boolean;
  can_change_sale_price?: boolean;
}

export interface CreateAuctionHouseResponse extends TransactionResponse {
  auction_house_address?: string;
  data?: AuctionHouseConfig;
}

// ===== LISTING TYPES =====
export type ListingStatus = "active" | "sold" | "cancelled" | "expired";

export interface NFTListing {
  listing_id: string;
  nft_mint: string;
  seller: string;
  price: number;
  currency_mint: string;
  status: ListingStatus;
  created_at: string;
  expires_at?: string;
  updated_at: string;
  auction_house?: string;
  metadata?: any;
}

export interface ListNFTRequest {
  nft_mint: string;
  price: number;
  duration_hours?: number;
  currency_mint?: string;
}

export interface ListNFTResponse extends TransactionResponse {
  data?: NFTListing;
}

// ===== BIDDING TYPES =====
export type BidStatus = "active" | "accepted" | "rejected" | "cancelled" | "expired";

export interface NFTBid {
  bid_id: string;
  nft_mint: string;
  bidder: string;
  price: number;
  currency_mint: string;
  status: BidStatus;
  created_at: string;
  expires_at?: string;
  updated_at: string;
  auction_house?: string;
}

export interface PlaceBidRequest {
  nft_mint: string;
  price: number;
  duration_hours?: number;
  currency_mint?: string;
}

export interface PlaceBidResponse extends TransactionResponse {
  data?: NFTBid;
}

// ===== PURCHASE TYPES =====
export interface PurchaseNFTRequest {
  nft_mint: string;
  max_price: number;
  currency_mint?: string;
}

// ===== SALE TYPES =====
export type SaleStatus = "pending" | "completed" | "failed" | "cancelled";

export interface NFTSale {
  sale_id: string;
  nft_mint: string;
  seller: string;
  buyer: string;
  price: number;
  currency_mint: string;
  status: SaleStatus;
  created_at: string;
  completed_at?: string;
  transaction_signature?: string;
  auction_house?: string;
  listing_id?: string;
  bid_id?: string;
  seller_fee?: number;
  buyer_fee?: number;
  royalty_fee?: number;
}

export interface SaleResponse extends TransactionResponse {
  data?: NFTSale;
}

// ===== DATA RESPONSE TYPES =====
export interface ListingsResponse {
  success: boolean;
  listings: NFTListing[];
  count: number;
}

export interface BidsResponse {
  success: boolean;
  bids: NFTBid[];
  count: number;
}

export interface SalesResponse {
  success: boolean;
  sales: NFTSale[];
  count: number;
}

export interface MarketplaceStats {
  active_listings: number;
  active_bids: number;
  total_sales: number;
  total_volume_sol: number;
  average_price_sol: number;
  auction_house_configured: boolean;
}

export interface MarketplaceStatsResponse {
  success: boolean;
  stats: MarketplaceStats;
}

// ===== WALLET TYPES =====
export interface WalletBalance {
  wallet_address: string;
  balance_sol: number;
  balance_lamports: number;
}

export interface WalletInfo {
  wallet_address: string;
  network: string;
  balance_sol: number;
}

// ===== HEALTH TYPES =====
export interface HealthResponse {
  status: string;
  message: string;
  version: string;
}

export interface StatusResponse {
  status: string;
  network: string;
  wallet_address: string;
  balance_sol: number;
}

// ===== API CLIENT INTERFACE =====
export interface SolanaNFTAPI {
  // Health
  getHealth(): Promise<HealthResponse>;
  getStatus(): Promise<StatusResponse>;
  
  // Wallet
  getWalletBalance(): Promise<WalletBalance>;
  getWalletInfo(): Promise<WalletInfo>;
  
  // Token & NFT Creation
  createToken(request: CreateTokenRequest): Promise<CreateTokenResponse>;
  createNFT(request: CreateNFTRequest): Promise<CreateNFTResponse>;
  createSimpleNFT(request: { name: string; symbol: string }): Promise<CreateTokenResponse>;
  
  // NFT Information
  getNFTInfo(mintAddress: string): Promise<any>;
  getAllNFTs(): Promise<any>;
  
  // Auction House
  createAuctionHouse(request: CreateAuctionHouseRequest): Promise<CreateAuctionHouseResponse>;
  getAuctionHouseInfo(): Promise<{ success: boolean; auction_house: AuctionHouseConfig }>;
  
  // Marketplace Operations
  listNFT(request: ListNFTRequest): Promise<ListNFTResponse>;
  placeBid(request: PlaceBidRequest): Promise<PlaceBidResponse>;
  purchaseNFT(request: PurchaseNFTRequest): Promise<SaleResponse>;
  
  // Management
  acceptBid(bidId: string): Promise<SaleResponse>;
  cancelListing(listingId: string): Promise<TransactionResponse>;
  cancelBid(bidId: string): Promise<TransactionResponse>;
  
  // Data
  getListings(nftMint?: string): Promise<ListingsResponse>;
  getBids(nftMint?: string): Promise<BidsResponse>;
  getSales(): Promise<SalesResponse>;
  getMarketplaceStats(): Promise<MarketplaceStatsResponse>;
}
