# Solana NFT Creator API

A production-ready Flask API for creating SPL tokens and NFTs on the Solana blockchain. Built with modern Python practices, comprehensive error handling, and Docker support.

## 🚀 Features

- **SPL Token Creation**: Create fungible and non-fungible tokens on Solana
- **Production Ready**: Structured codebase with proper error handling and logging
- **Docker Support**: Easy deployment with Docker and Docker Compose
- **Comprehensive API**: RESTful endpoints with proper validation
- **Multi-Network**: Support for Devnet, Testnet, and Mainnet
- **Wallet Management**: Secure wallet operations and balance checking
- **Health Monitoring**: Health check endpoints for monitoring

## 📁 Project Structure

```
├── src/                          # Source code
│   ├── app/                      # Flask application factory
│   ├── config/                   # Configuration management
│   ├── models/                   # Data models and schemas
│   ├── routes/                   # API route handlers
│   ├── services/                 # Business logic services
│   └── utils/                    # Utilities and helpers
├── tests/                        # Test suite
├── docs/                         # Documentation
├── logs/                         # Application logs
├── main.py                       # Application entry point
├── requirements.txt              # Python dependencies
├── Dockerfile                    # Docker configuration
├── docker-compose.yml           # Docker Compose for production
├── docker-compose.simple.yml    # Docker Compose for development
└── .env                         # Environment variables
```

## 🛠️ Quick Start

### Local Development

1. **Setup environment**
   ```bash
   python -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

2. **Configure wallet**
   ```bash
   # Make sure you have wallet.json in app/ directory
   ```

3. **Run application**
   ```bash
   python main.py
   ```

### Docker (Recommended)

```bash
# Simple deployment
docker compose -f docker-compose.simple.yml up -d

# Check logs
docker compose -f docker-compose.simple.yml logs -f
```

## 📚 API Usage

### Base URL
```
http://localhost:5002
```

### Create Token
```bash
curl -X POST http://localhost:5002/api/v1/create_token \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Token",
    "symbol": "MTK",
    "amount": 1
  }'
```

### Check Health
```bash
curl http://localhost:5002/health
```

### Check Wallet Balance
```bash
curl http://localhost:5002/api/v1/wallet/balance
```

## 🔧 Configuration

Key environment variables in `.env`:

```bash
# Network
SOLANA_RPC_URL=https://api.devnet.solana.com
SOLANA_NETWORK=devnet

# Wallet
SOLANA_WALLET_FILE_PATH=app/wallet.json

# API
API_HOST=0.0.0.0
API_PORT=5002
```

## 🎯 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Health check |
| GET | `/status` | Detailed status |
| POST | `/api/v1/create_token` | Create SPL token |
| GET | `/api/v1/wallet/balance` | Get wallet balance |
| GET | `/api/v1/wallet/info` | Get wallet info |
| GET | `/api/v1/token/{mint}` | Get token info |

## 🧪 Testing

```bash
# Test token creation
curl -X POST http://localhost:5002/api/v1/create_token \
  -H "Content-Type: application/json" \
  -d '{"name": "Test Token", "symbol": "TEST", "amount": 1}'
```

## 🚀 Production Deployment

1. **Set environment to production**
   ```bash
   FLASK_ENV=production
   SOLANA_NETWORK=mainnet-beta
   SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
   ```

2. **Deploy with Docker**
   ```bash
   docker compose build
   docker compose up -d
   ```

3. **Monitor**
   ```bash
   docker compose logs -f
   curl http://localhost:5002/health
   ```

## 🔍 Architecture

### Service Layer
- **WalletService**: Wallet operations and management
- **SolanaService**: Blockchain interactions and transactions
- **TokenService**: Token creation and management

### Models
- **TokenRequest**: Input validation and data structure
- **TokenResponse**: API response formatting
- **TransactionResult**: Transaction status and details

### Error Handling
- Comprehensive exception handling
- Structured error responses
- Detailed logging for debugging

## 📊 Monitoring

### Health Checks
- Basic health: `/health`
- Detailed status: `/status`

### Logs
- Application logs in `logs/app.log`
- Docker logs via `docker compose logs`

## 🔒 Security

- Secure wallet file handling
- Environment variable configuration
- Input validation and sanitization
- Rate limiting (configurable)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Submit pull request

## 📄 License

MIT License

## 🆘 Support

- Check logs for error details
- Review API documentation
- Test on devnet first
- Ensure sufficient SOL balance

---

**Ready to create tokens on Solana!** 🚀
