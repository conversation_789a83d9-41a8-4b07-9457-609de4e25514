# app_simple.py
# Simplified Flask application for creating NFTs on Solana blockchain

import os
import json
import struct
from flask import Flask, request, jsonify
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solders.system_program import ID as SYSTEM_PROGRAM_ID
from solana.rpc.api import Client
from solana.rpc.commitment import Confirmed
from solders.instruction import Instruction, AccountMeta
from solders.message import Message
from solders.transaction import VersionedTransaction

# Constants
TOKEN_PROGRAM_ID = Pubkey.from_string('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA')
METADATA_PROGRAM_ID = Pubkey.from_string('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s')
SYSVAR_RENT_PUBKEY = Pubkey.from_string('SysvarRent111111111111111111111111111111111')

# Load environment variables for configuration
SOLANA_RPC_URL = os.getenv('SOLANA_RPC_URL', 'https://api.devnet.solana.com')
WALLET_FILE_PATH = os.getenv('SOLANA_WALLET_FILE_PATH', 'app/wallet.json')

app = Flask(__name__)

# --- Helper Functions ---
def load_payer_keypair():
    """Loads the payer keypair from the specified JSON file."""
    try:
        with open(WALLET_FILE_PATH, 'r') as f:
            private_key_list = json.load(f)
        if isinstance(private_key_list, list) and all(isinstance(x, int) for x in private_key_list):
            return Keypair.from_bytes(bytes(private_key_list))
        else:
            return Keypair.from_base58_string(str(private_key_list))
    except FileNotFoundError:
        app.logger.error(f"Wallet file not found at {WALLET_FILE_PATH}")
        return None
    except Exception as e:
        app.logger.error(f"Error loading keypair: {e}")
        return None

def get_metadata_pda(mint_pubkey: Pubkey) -> Pubkey:
    """Derives the Metaplex Metadata PDA for a given mint."""
    return Pubkey.find_program_address(
        [b"metadata", bytes(METADATA_PROGRAM_ID), bytes(mint_pubkey)],
        METADATA_PROGRAM_ID
    )[0]

def create_mint_instruction(payer: Pubkey, mint: Pubkey, mint_authority: Pubkey, freeze_authority: Pubkey = None):
    """Create instruction to create a mint account"""
    # This is a simplified version - in production you'd use proper SPL token instructions
    space = 82  # Mint account size
    lamports = 1461600  # Rent exemption for mint account (approximate)
    
    # Create account instruction
    create_account_ix = Instruction(
        program_id=SYSTEM_PROGRAM_ID,
        accounts=[
            AccountMeta(pubkey=payer, is_signer=True, is_writable=True),
            AccountMeta(pubkey=mint, is_signer=True, is_writable=True),
        ],
        data=struct.pack('<I', 0) + struct.pack('<Q', lamports) + struct.pack('<Q', space) + bytes(TOKEN_PROGRAM_ID)
    )
    
    # Initialize mint instruction
    freeze_auth_bytes = bytes(freeze_authority) if freeze_authority else b'\x00' * 32
    init_mint_data = struct.pack('<B', 0) + struct.pack('<B', 0) + bytes(mint_authority) + b'\x01' + freeze_auth_bytes
    
    init_mint_ix = Instruction(
        program_id=TOKEN_PROGRAM_ID,
        accounts=[
            AccountMeta(pubkey=mint, is_signer=False, is_writable=True),
            AccountMeta(pubkey=SYSVAR_RENT_PUBKEY, is_signer=False, is_writable=False),
        ],
        data=init_mint_data
    )
    
    return [create_account_ix, init_mint_ix]

def create_simple_metadata_instruction(metadata_pda: Pubkey, mint: Pubkey, mint_authority: Pubkey, payer: Pubkey, name: str, symbol: str, uri: str):
    """Create a simplified metadata instruction"""
    # This is a very simplified version of metadata creation
    # In production, you'd use proper Metaplex instruction builders
    
    # Simplified metadata data
    metadata_data = name.encode('utf-8')[:32].ljust(32, b'\x00')
    metadata_data += symbol.encode('utf-8')[:10].ljust(10, b'\x00')
    metadata_data += uri.encode('utf-8')[:200].ljust(200, b'\x00')
    
    return Instruction(
        program_id=METADATA_PROGRAM_ID,
        accounts=[
            AccountMeta(pubkey=metadata_pda, is_signer=False, is_writable=True),
            AccountMeta(pubkey=mint, is_signer=False, is_writable=False),
            AccountMeta(pubkey=mint_authority, is_signer=True, is_writable=False),
            AccountMeta(pubkey=payer, is_signer=True, is_writable=True),
            AccountMeta(pubkey=mint_authority, is_signer=True, is_writable=False),
            AccountMeta(pubkey=SYSTEM_PROGRAM_ID, is_signer=False, is_writable=False),
            AccountMeta(pubkey=SYSVAR_RENT_PUBKEY, is_signer=False, is_writable=False),
        ],
        data=b'\x21' + metadata_data  # 0x21 is CreateMetadataAccountV3 discriminator
    )

# --- API Endpoint ---
@app.route('/create_nft', methods=['POST'])
def create_nft_endpoint():
    app.logger.info("Received request to /create_nft")
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400

        nft_name = data.get('name')
        nft_symbol = data.get('symbol')
        metadata_uri = data.get('metadata_uri')
        seller_fee_basis_points = data.get('seller_fee_basis_points')
        is_mutable = data.get('is_mutable', True)

        if not all([nft_name, nft_symbol, metadata_uri, isinstance(seller_fee_basis_points, int)]):
            return jsonify({"error": "Missing required fields: name, symbol, metadata_uri, seller_fee_basis_points"}), 400

        app.logger.info(f"NFT Details: Name={nft_name}, Symbol={nft_symbol}, URI={metadata_uri}")

    except Exception as e:
        app.logger.error(f"Error parsing request: {e}")
        return jsonify({"error": f"Error parsing request: {e}"}), 400

    payer_keypair = load_payer_keypair()
    if not payer_keypair:
        return jsonify({"error": "Failed to load payer wallet"}), 500
    
    app.logger.info(f"Payer wallet loaded: {payer_keypair.pubkey()}")

    solana_client = Client(SOLANA_RPC_URL, commitment=Confirmed)

    try:
        # Create a new Mint Account
        mint_keypair = Keypair()
        mint_pubkey = mint_keypair.pubkey()
        app.logger.info(f"New Mint Account Pubkey: {mint_pubkey}")

        # Create mint instructions
        mint_instructions = create_mint_instruction(
            payer_keypair.pubkey(),
            mint_pubkey,
            payer_keypair.pubkey(),
            payer_keypair.pubkey()
        )

        # Create Metaplex Metadata Account
        metadata_pda = get_metadata_pda(mint_pubkey)
        app.logger.info(f"Metaplex Metadata PDA: {metadata_pda}")

        metadata_ix = create_simple_metadata_instruction(
            metadata_pda,
            mint_pubkey,
            payer_keypair.pubkey(),
            payer_keypair.pubkey(),
            nft_name,
            nft_symbol,
            metadata_uri
        )

        # Assemble Transaction
        all_instructions = mint_instructions + [metadata_ix]

        # Get the latest blockhash
        blockhash_resp = solana_client.get_latest_blockhash()

        # Create message and versioned transaction
        message = Message.new_with_blockhash(
            instructions=all_instructions,
            payer_pubkey=payer_keypair.pubkey(),
            recent_blockhash=blockhash_resp.value.blockhash
        )

        # Create versioned transaction
        transaction = VersionedTransaction(message, [payer_keypair, mint_keypair])

        app.logger.info("Sending transaction...")
        resp = solana_client.send_transaction(transaction)
        tx_signature = resp.value
        app.logger.info(f"Transaction sent. Signature: {tx_signature}")

        # Confirm transaction
        solana_client.confirm_transaction(tx_signature, commitment=Confirmed)
        app.logger.info(f"Transaction confirmed: {tx_signature}")

        return jsonify({
            "message": "NFT created successfully!",
            "nft_mint_address": str(mint_pubkey),
            "metadata_pda": str(metadata_pda),
            "transaction_signature": str(tx_signature),
            "explorer_url": f"https://explorer.solana.com/tx/{tx_signature}?cluster=devnet"
        }), 201

    except Exception as e:
        app.logger.error(f"Error during NFT creation: {e}", exc_info=True)
        return jsonify({"error": "Failed to create NFT", "details": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy", "message": "Solana NFT Creator API is running"}), 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
