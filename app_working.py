# app_working.py
# Working Flask application for creating SPL tokens (simplified NFT) on Solana blockchain

import os
import json
import struct
from flask import Flask, request, jsonify
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solders.system_program import ID as SYSTEM_PROGRAM_ID
from solana.rpc.api import Client
from solana.rpc.commitment import Confirmed
from solders.instruction import Instruction, AccountMeta
from solders.message import Message
from solders.transaction import VersionedTransaction

# Constants
TOKEN_PROGRAM_ID = Pubkey.from_string('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA')
ASSOCIATED_TOKEN_PROGRAM_ID = Pubkey.from_string('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL')
SYSVAR_RENT_PUBKEY = Pubkey.from_string('SysvarRent111111111111111111111111111111111')

# Load environment variables for configuration
SOLANA_RPC_URL = os.getenv('SOLANA_RPC_URL', 'https://api.devnet.solana.com')
WALLET_FILE_PATH = os.getenv('SOLANA_WALLET_FILE_PATH', 'app/wallet.json')

app = Flask(__name__)

# --- Helper Functions ---
def load_payer_keypair():
    """Loads the payer keypair from the specified JSON file."""
    try:
        with open(WALLET_FILE_PATH, 'r') as f:
            private_key_list = json.load(f)
        if isinstance(private_key_list, list) and all(isinstance(x, int) for x in private_key_list):
            return Keypair.from_bytes(bytes(private_key_list))
        else:
            return Keypair.from_base58_string(str(private_key_list))
    except FileNotFoundError:
        app.logger.error(f"Wallet file not found at {WALLET_FILE_PATH}")
        return None
    except Exception as e:
        app.logger.error(f"Error loading keypair: {e}")
        return None

def get_associated_token_address(owner: Pubkey, mint: Pubkey) -> Pubkey:
    """Get associated token account address"""
    return Pubkey.find_program_address(
        [bytes(owner), bytes(TOKEN_PROGRAM_ID), bytes(mint)],
        ASSOCIATED_TOKEN_PROGRAM_ID
    )[0]

def create_mint_instructions(payer: Pubkey, mint: Pubkey, mint_authority: Pubkey, freeze_authority: Pubkey = None):
    """Create instructions to create and initialize a mint account"""
    space = 82  # Mint account size
    lamports = 1461600  # Rent exemption for mint account
    
    # Create account instruction
    create_account_data = struct.pack('<I', 0)  # CreateAccount instruction
    create_account_data += struct.pack('<Q', lamports)  # lamports
    create_account_data += struct.pack('<Q', space)  # space
    create_account_data += bytes(TOKEN_PROGRAM_ID)  # owner
    
    create_account_ix = Instruction(
        program_id=SYSTEM_PROGRAM_ID,
        accounts=[
            AccountMeta(pubkey=payer, is_signer=True, is_writable=True),
            AccountMeta(pubkey=mint, is_signer=True, is_writable=True),
        ],
        data=create_account_data
    )
    
    # Initialize mint instruction
    init_mint_data = struct.pack('<B', 0)  # InitializeMint instruction
    init_mint_data += struct.pack('<B', 0)  # decimals (0 for NFT)
    init_mint_data += bytes(mint_authority)  # mint authority
    init_mint_data += b'\x01'  # freeze authority option (Some)
    init_mint_data += bytes(freeze_authority) if freeze_authority else bytes(mint_authority)
    
    init_mint_ix = Instruction(
        program_id=TOKEN_PROGRAM_ID,
        accounts=[
            AccountMeta(pubkey=mint, is_signer=False, is_writable=True),
            AccountMeta(pubkey=SYSVAR_RENT_PUBKEY, is_signer=False, is_writable=False),
        ],
        data=init_mint_data
    )
    
    return [create_account_ix, init_mint_ix]

def create_associated_token_account_instruction(payer: Pubkey, owner: Pubkey, mint: Pubkey):
    """Create instruction to create associated token account"""
    ata_address = get_associated_token_address(owner, mint)
    
    return Instruction(
        program_id=ASSOCIATED_TOKEN_PROGRAM_ID,
        accounts=[
            AccountMeta(pubkey=payer, is_signer=True, is_writable=True),
            AccountMeta(pubkey=ata_address, is_signer=False, is_writable=True),
            AccountMeta(pubkey=owner, is_signer=False, is_writable=False),
            AccountMeta(pubkey=mint, is_signer=False, is_writable=False),
            AccountMeta(pubkey=SYSTEM_PROGRAM_ID, is_signer=False, is_writable=False),
            AccountMeta(pubkey=TOKEN_PROGRAM_ID, is_signer=False, is_writable=False),
            AccountMeta(pubkey=SYSVAR_RENT_PUBKEY, is_signer=False, is_writable=False),
        ],
        data=b''  # No data needed for ATA creation
    )

def create_mint_to_instruction(mint: Pubkey, dest: Pubkey, authority: Pubkey, amount: int):
    """Create instruction to mint tokens"""
    mint_to_data = struct.pack('<B', 7)  # MintTo instruction
    mint_to_data += struct.pack('<Q', amount)  # amount
    
    return Instruction(
        program_id=TOKEN_PROGRAM_ID,
        accounts=[
            AccountMeta(pubkey=mint, is_signer=False, is_writable=True),
            AccountMeta(pubkey=dest, is_signer=False, is_writable=True),
            AccountMeta(pubkey=authority, is_signer=True, is_writable=False),
        ],
        data=mint_to_data
    )

# --- API Endpoint ---
@app.route('/create_token', methods=['POST'])
def create_token_endpoint():
    app.logger.info("Received request to /create_token")
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400

        token_name = data.get('name', 'Test Token')
        token_symbol = data.get('symbol', 'TEST')
        amount = data.get('amount', 1)  # Amount to mint

        app.logger.info(f"Token Details: Name={token_name}, Symbol={token_symbol}, Amount={amount}")

    except Exception as e:
        app.logger.error(f"Error parsing request: {e}")
        return jsonify({"error": f"Error parsing request: {e}"}), 400

    payer_keypair = load_payer_keypair()
    if not payer_keypair:
        return jsonify({"error": "Failed to load payer wallet"}), 500
    
    app.logger.info(f"Payer wallet loaded: {payer_keypair.pubkey()}")

    solana_client = Client(SOLANA_RPC_URL, commitment=Confirmed)

    try:
        # Create a new Mint Account
        mint_keypair = Keypair()
        mint_pubkey = mint_keypair.pubkey()
        app.logger.info(f"New Mint Account Pubkey: {mint_pubkey}")

        # Create mint instructions
        mint_instructions = create_mint_instructions(
            payer_keypair.pubkey(),
            mint_pubkey,
            payer_keypair.pubkey(),
            payer_keypair.pubkey()
        )

        # Create Associated Token Account
        ata_address = get_associated_token_address(payer_keypair.pubkey(), mint_pubkey)
        create_ata_ix = create_associated_token_account_instruction(
            payer_keypair.pubkey(),
            payer_keypair.pubkey(),
            mint_pubkey
        )

        # Mint tokens to ATA
        mint_to_ix = create_mint_to_instruction(
            mint_pubkey,
            ata_address,
            payer_keypair.pubkey(),
            amount
        )

        # Assemble all instructions
        all_instructions = mint_instructions + [create_ata_ix, mint_to_ix]
        
        # Get the latest blockhash
        blockhash_resp = solana_client.get_latest_blockhash()
        
        # Create message and versioned transaction
        message = Message.new_with_blockhash(
            all_instructions,
            payer_keypair.pubkey(),
            blockhash_resp.value.blockhash
        )
        
        # Create versioned transaction
        transaction = VersionedTransaction(message, [payer_keypair, mint_keypair])

        app.logger.info("Sending transaction...")
        resp = solana_client.send_transaction(transaction)
        tx_signature = resp.value
        app.logger.info(f"Transaction sent. Signature: {tx_signature}")

        # Confirm transaction
        solana_client.confirm_transaction(tx_signature, commitment=Confirmed)
        app.logger.info(f"Transaction confirmed: {tx_signature}")

        return jsonify({
            "message": "Token created successfully!",
            "token_mint_address": str(mint_pubkey),
            "associated_token_account": str(ata_address),
            "transaction_signature": str(tx_signature),
            "explorer_url": f"https://explorer.solana.com/tx/{tx_signature}?cluster=devnet",
            "token_details": {
                "name": token_name,
                "symbol": token_symbol,
                "amount_minted": amount,
                "decimals": 0
            }
        }), 201

    except Exception as e:
        app.logger.error(f"Error during token creation: {e}", exc_info=True)
        return jsonify({"error": "Failed to create token", "details": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy", "message": "Solana Token Creator API is running"}), 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5002, debug=True)
