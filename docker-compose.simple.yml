version: "3.8"

services:
  solana-nft-api:
    image: python:3.11-slim
    container_name: solana-nft-creator
    working_dir: /app
    ports:
      - "5001:5001"
    volumes:
      # Mount the entire project directory
      - .:/app
      # Mount wallet file specifically
      - ./app/wallet.json:/app/app/wallet.json:ro
    environment:
      - SOLANA_RPC_URL=https://api.devnet.solana.com
      - SOLANA_WALLET_FILE_PATH=/app/app/wallet.json
      - FLASK_ENV=development
      - PYTHONUNBUFFERED=1
    command: >
      bash -c "
        apt-get update && 
        apt-get install -y curl &&
        pip install --no-cache-dir flask flask-cors solders solana borsh-construct python-dotenv &&
        python app_working.py
      "
    restart: unless-stopped
    networks:
      - solana-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  solana-network:
    driver: bridge
