version: '3.8'

services:
  solana-nft-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: solana-nft-creator
    ports:
      - "5000:5000"
    volumes:
      # Mount wallet file (read-only for security)
      - ./app/wallet.json:/app/app/wallet.json:ro
      # Optional: Mount logs directory
      - ./logs:/app/logs
    environment:
      - SOLANA_RPC_URL=https://api.devnet.solana.com
      - SOLANA_WALLET_FILE_PATH=/app/app/wallet.json
      - FLASK_ENV=development
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    networks:
      - solana-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a simple web interface (if needed later)
  # solana-nft-frontend:
  #   image: nginx:alpine
  #   container_name: solana-nft-frontend
  #   ports:
  #     - "8080:80"
  #   volumes:
  #     - ./frontend:/usr/share/nginx/html:ro
  #   depends_on:
  #     - solana-nft-api
  #   networks:
  #     - solana-network

networks:
  solana-network:
    driver: bridge

volumes:
  logs:
    driver: local
