#!/bin/bash

# Docker management script for Solana NFT Creator

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if wallet file exists
check_wallet() {
    if [ ! -f "app/wallet.json" ]; then
        print_error "Wallet file not found at app/wallet.json"
        print_info "Please ensure your Solana wallet keypair is available"
        exit 1
    fi
    print_success "Wallet file found"
}

# Build Docker image
build() {
    print_info "Building Docker image..."
    docker compose build
    print_success "Docker image built successfully"
}

# Start services
start() {
    check_wallet
    print_info "Starting Solana NFT Creator API..."
    docker compose up -d
    print_success "Services started successfully"
    print_info "API is available at: http://localhost:5000"
    print_info "Health check: http://localhost:5000/health"
}

# Stop services
stop() {
    print_info "Stopping services..."
    docker compose down
    print_success "Services stopped"
}

# Restart services
restart() {
    print_info "Restarting services..."
    stop
    start
}

# View logs
logs() {
    print_info "Showing logs (Press Ctrl+C to exit)..."
    docker compose logs -f solana-nft-api
}

# Show status
status() {
    print_info "Service status:"
    docker compose ps
}

# Clean up
clean() {
    print_warning "This will remove all containers, images, and volumes"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Cleaning up..."
        docker compose down -v --rmi all
        print_success "Cleanup completed"
    else
        print_info "Cleanup cancelled"
    fi
}

# Test API
test() {
    print_info "Testing API..."
    
    # Health check
    print_info "Testing health endpoint..."
    if curl -s http://localhost:5000/health > /dev/null; then
        print_success "Health check passed"
    else
        print_error "Health check failed"
        return 1
    fi
    
    # Create token test
    print_info "Testing token creation..."
    response=$(curl -s -X POST http://localhost:5000/create_token \
        -H "Content-Type: application/json" \
        -d '{
            "name": "Docker Test Token",
            "symbol": "DTT",
            "amount": 1
        }')
    
    if echo "$response" | grep -q "Token created successfully"; then
        print_success "Token creation test passed"
        echo "$response" | jq '.'
    else
        print_error "Token creation test failed"
        echo "$response"
        return 1
    fi
}

# Help
help() {
    echo "Solana NFT Creator Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build     Build Docker image"
    echo "  start     Start services"
    echo "  stop      Stop services"
    echo "  restart   Restart services"
    echo "  logs      View logs"
    echo "  status    Show service status"
    echo "  test      Test API endpoints"
    echo "  clean     Clean up all Docker resources"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start    # Start the API"
    echo "  $0 logs     # View real-time logs"
    echo "  $0 test     # Test API functionality"
}

# Main script logic
case "${1:-help}" in
    build)
        build
        ;;
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    logs)
        logs
        ;;
    status)
        status
        ;;
    test)
        test
        ;;
    clean)
        clean
        ;;
    help|*)
        help
        ;;
esac
