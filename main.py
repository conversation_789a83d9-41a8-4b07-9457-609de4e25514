# main.py - Main entry point
import os
from dotenv import load_dotenv
from src.app import create_app
from src.utils.logger import get_logger

# Load environment variables
load_dotenv()

# Create Flask application
app = create_app()
logger = get_logger(__name__)

if __name__ == '__main__':
    # Get configuration from environment
    host = app.config.get('API_HOST', '0.0.0.0')
    port = app.config.get('API_PORT', 5000)
    debug = app.config.get('DEBUG', False)
    
    logger.info(f"Starting Solana NFT Creator API on {host}:{port}")
    logger.info(f"Network: {app.config.get('SOLANA_NETWORK', 'devnet')}")
    logger.info(f"Debug mode: {debug}")
    
    app.run(host=host, port=port, debug=debug)
