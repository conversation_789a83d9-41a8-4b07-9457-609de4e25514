# src/app/__init__.py
import os
from flask import Flask
from flask_cors import CORS
from ..config import config
from ..routes import token_bp, health_bp, nft_bp, marketplace_bp
from ..utils.logger import setup_logger

def create_app(config_name=None):
    """Application factory pattern"""
    
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    
    # Load configuration
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # Setup logging
    setup_logger(
        log_file=app.config.get('LOG_FILE'),
        log_level=app.config.get('LOG_LEVEL', 'INFO')
    )
    
    # Setup CORS
    CORS(app, origins=app.config.get('CORS_ORIGINS', ['*']))
    
    # Register blueprints
    api_prefix = app.config.get('API_PREFIX', '/api/v1')
    
    app.register_blueprint(health_bp)
    app.register_blueprint(token_bp, url_prefix=api_prefix)
    app.register_blueprint(nft_bp, url_prefix=api_prefix)
    app.register_blueprint(marketplace_bp, url_prefix=api_prefix + '/marketplace')
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return {"error": "Not found"}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return {"error": "Internal server error"}, 500
    
    @app.errorhandler(400)
    def bad_request(error):
        return {"error": "Bad request"}, 400
    
    return app
