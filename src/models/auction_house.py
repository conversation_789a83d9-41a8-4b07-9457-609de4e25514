# src/models/auction_house.py
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class ListingStatus(Enum):
    """Listing status enumeration"""
    ACTIVE = "active"
    SOLD = "sold"
    CANCELLED = "cancelled"
    EXPIRED = "expired"

class BidStatus(Enum):
    """Bid status enumeration"""
    ACTIVE = "active"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    CANCELLED = "cancelled"
    EXPIRED = "expired"

class SaleStatus(Enum):
    """Sale status enumeration"""
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class AuctionHouseConfig:
    """Auction House configuration model"""
    authority: str  # Auction House authority public key
    treasury_mint: str  # Treasury mint (usually SOL or USDC)
    fee_withdrawal_destination: str  # Where fees go
    treasury_withdrawal_destination: str  # Where treasury funds go
    seller_fee_basis_points: int  # Fee charged to sellers (in basis points)
    requires_sign_off: bool = False  # Whether sales require authority sign-off
    can_change_sale_price: bool = True  # Whether sellers can change price
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "authority": self.authority,
            "treasury_mint": self.treasury_mint,
            "fee_withdrawal_destination": self.fee_withdrawal_destination,
            "treasury_withdrawal_destination": self.treasury_withdrawal_destination,
            "seller_fee_basis_points": self.seller_fee_basis_points,
            "requires_sign_off": self.requires_sign_off,
            "can_change_sale_price": self.can_change_sale_price
        }

@dataclass
class NFTListing:
    """NFT listing model"""
    listing_id: str
    nft_mint: str
    seller: str
    price: float  # Price in SOL
    currency_mint: str = "So11111111111111111111111111111111111111112"  # SOL mint
    status: ListingStatus = ListingStatus.ACTIVE
    created_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    auction_house: Optional[str] = None
    seller_trade_state: Optional[str] = None
    free_seller_trade_state: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "listing_id": self.listing_id,
            "nft_mint": self.nft_mint,
            "seller": self.seller,
            "price": self.price,
            "currency_mint": self.currency_mint,
            "status": self.status.value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "auction_house": self.auction_house,
            "seller_trade_state": self.seller_trade_state,
            "free_seller_trade_state": self.free_seller_trade_state,
            "metadata": self.metadata
        }

@dataclass
class NFTBid:
    """NFT bid model"""
    bid_id: str
    nft_mint: str
    bidder: str
    price: float  # Bid price in SOL
    currency_mint: str = "So11111111111111111111111111111111111111112"  # SOL mint
    status: BidStatus = BidStatus.ACTIVE
    created_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    auction_house: Optional[str] = None
    buyer_trade_state: Optional[str] = None
    escrow_payment_account: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "bid_id": self.bid_id,
            "nft_mint": self.nft_mint,
            "bidder": self.bidder,
            "price": self.price,
            "currency_mint": self.currency_mint,
            "status": self.status.value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "auction_house": self.auction_house,
            "buyer_trade_state": self.buyer_trade_state,
            "escrow_payment_account": self.escrow_payment_account
        }

@dataclass
class NFTSale:
    """NFT sale model"""
    sale_id: str
    nft_mint: str
    seller: str
    buyer: str
    price: float  # Sale price in SOL
    currency_mint: str = "So11111111111111111111111111111111111111112"  # SOL mint
    status: SaleStatus = SaleStatus.PENDING
    created_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    transaction_signature: Optional[str] = None
    auction_house: Optional[str] = None
    listing_id: Optional[str] = None
    bid_id: Optional[str] = None
    seller_fee: Optional[float] = None
    buyer_fee: Optional[float] = None
    royalty_fee: Optional[float] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "sale_id": self.sale_id,
            "nft_mint": self.nft_mint,
            "seller": self.seller,
            "buyer": self.buyer,
            "price": self.price,
            "currency_mint": self.currency_mint,
            "status": self.status.value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "transaction_signature": self.transaction_signature,
            "auction_house": self.auction_house,
            "listing_id": self.listing_id,
            "bid_id": self.bid_id,
            "seller_fee": self.seller_fee,
            "buyer_fee": self.buyer_fee,
            "royalty_fee": self.royalty_fee
        }

@dataclass
class ListingRequest:
    """Request model for creating NFT listing"""
    nft_mint: str
    price: float
    duration_hours: Optional[int] = None  # Listing duration in hours
    currency_mint: str = "So11111111111111111111111111111111111111112"  # SOL mint
    
    def validate(self) -> List[str]:
        """Validate listing request"""
        errors = []
        
        if not self.nft_mint or len(self.nft_mint.strip()) == 0:
            errors.append("NFT mint address is required")
        
        if self.price <= 0:
            errors.append("Price must be greater than 0")
        
        if self.duration_hours is not None and self.duration_hours <= 0:
            errors.append("Duration must be greater than 0 hours")
        
        return errors

@dataclass
class BidRequest:
    """Request model for placing NFT bid"""
    nft_mint: str
    price: float
    duration_hours: Optional[int] = None  # Bid duration in hours
    currency_mint: str = "So11111111111111111111111111111111111111112"  # SOL mint
    
    def validate(self) -> List[str]:
        """Validate bid request"""
        errors = []
        
        if not self.nft_mint or len(self.nft_mint.strip()) == 0:
            errors.append("NFT mint address is required")
        
        if self.price <= 0:
            errors.append("Bid price must be greater than 0")
        
        if self.duration_hours is not None and self.duration_hours <= 0:
            errors.append("Duration must be greater than 0 hours")
        
        return errors

@dataclass
class PurchaseRequest:
    """Request model for direct NFT purchase"""
    nft_mint: str
    max_price: float  # Maximum price willing to pay
    currency_mint: str = "So11111111111111111111111111111111111111112"  # SOL mint
    
    def validate(self) -> List[str]:
        """Validate purchase request"""
        errors = []
        
        if not self.nft_mint or len(self.nft_mint.strip()) == 0:
            errors.append("NFT mint address is required")
        
        if self.max_price <= 0:
            errors.append("Maximum price must be greater than 0")
        
        return errors

@dataclass
class AuctionHouseResponse:
    """Response model for auction house operations"""
    success: bool
    message: str
    auction_house_address: Optional[str] = None
    transaction_signature: Optional[str] = None
    explorer_url: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "success": self.success,
            "message": self.message
        }
        
        if self.auction_house_address:
            result["auction_house_address"] = self.auction_house_address
        if self.transaction_signature:
            result["transaction_signature"] = self.transaction_signature
        if self.explorer_url:
            result["explorer_url"] = self.explorer_url
        if self.data:
            result["data"] = self.data
        if self.created_at:
            result["created_at"] = self.created_at.isoformat()
        
        return result
