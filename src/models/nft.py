# src/models/nft.py
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime

@dataclass
class NFTAttribute:
    """NFT attribute model"""
    trait_type: str
    value: str
    display_type: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "trait_type": self.trait_type,
            "value": self.value
        }
        if self.display_type:
            result["display_type"] = self.display_type
        return result

@dataclass
class NFTFile:
    """NFT file model"""
    uri: str
    type: str
    cdn: Optional[bool] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "uri": self.uri,
            "type": self.type
        }
        if self.cdn is not None:
            result["cdn"] = self.cdn
        return result

@dataclass
class NFTCreator:
    """NFT creator model"""
    address: str
    share: int
    verified: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "address": self.address,
            "share": self.share,
            "verified": self.verified
        }

@dataclass
class NFTCollection:
    """NFT collection model"""
    name: str
    family: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {"name": self.name}
        if self.family:
            result["family"] = self.family
        return result

@dataclass
class NFTProperties:
    """NFT properties model"""
    files: List[NFTFile] = field(default_factory=list)
    category: str = "image"
    creators: List[NFTCreator] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "files": [f.to_dict() for f in self.files],
            "category": self.category,
            "creators": [c.to_dict() for c in self.creators]
        }

@dataclass
class NFTMetadata:
    """Complete NFT metadata model following Metaplex standard"""
    name: str
    symbol: str
    description: str
    image: str
    seller_fee_basis_points: int = 0
    external_url: Optional[str] = None
    animation_url: Optional[str] = None
    attributes: List[NFTAttribute] = field(default_factory=list)
    properties: Optional[NFTProperties] = None
    collection: Optional[NFTCollection] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "name": self.name,
            "symbol": self.symbol,
            "description": self.description,
            "image": self.image,
            "seller_fee_basis_points": self.seller_fee_basis_points
        }
        
        if self.external_url:
            result["external_url"] = self.external_url
        if self.animation_url:
            result["animation_url"] = self.animation_url
        if self.attributes:
            result["attributes"] = [attr.to_dict() for attr in self.attributes]
        if self.properties:
            result["properties"] = self.properties.to_dict()
        if self.collection:
            result["collection"] = self.collection.to_dict()
            
        return result

@dataclass
class NFTRequest:
    """NFT creation request model"""
    name: str
    symbol: str
    description: str
    image_url: str
    external_url: Optional[str] = None
    animation_url: Optional[str] = None
    attributes: List[Dict[str, Any]] = field(default_factory=list)
    collection_name: Optional[str] = None
    collection_family: Optional[str] = None
    seller_fee_basis_points: int = 0
    is_mutable: bool = True
    max_supply: Optional[int] = None
    
    def validate(self) -> List[str]:
        """Validate NFT request data"""
        errors = []
        
        if not self.name or len(self.name.strip()) == 0:
            errors.append("NFT name is required")
        elif len(self.name) > 32:
            errors.append("NFT name must be 32 characters or less")
            
        if not self.symbol or len(self.symbol.strip()) == 0:
            errors.append("NFT symbol is required")
        elif len(self.symbol) > 10:
            errors.append("NFT symbol must be 10 characters or less")
            
        if not self.description or len(self.description.strip()) == 0:
            errors.append("NFT description is required")
        elif len(self.description) > 2000:
            errors.append("NFT description must be 2000 characters or less")
            
        if not self.image_url or not self.image_url.startswith(('http://', 'https://')):
            errors.append("Valid image URL is required")
            
        if self.external_url and not self.external_url.startswith(('http://', 'https://')):
            errors.append("External URL must be a valid URL")
            
        if self.animation_url and not self.animation_url.startswith(('http://', 'https://')):
            errors.append("Animation URL must be a valid URL")
            
        if self.seller_fee_basis_points < 0 or self.seller_fee_basis_points > 10000:
            errors.append("Seller fee basis points must be between 0 and 10000")
            
        if self.max_supply is not None and self.max_supply <= 0:
            errors.append("Max supply must be greater than 0")
            
        return errors
    
    def to_nft_metadata(self, creator_address: str) -> NFTMetadata:
        """Convert to NFTMetadata object"""
        # Convert attributes
        attributes = []
        for attr in self.attributes:
            if isinstance(attr, dict) and 'trait_type' in attr and 'value' in attr:
                attributes.append(NFTAttribute(
                    trait_type=attr['trait_type'],
                    value=str(attr['value']),
                    display_type=attr.get('display_type')
                ))
        
        # Create properties
        properties = NFTProperties(
            files=[NFTFile(uri=self.image_url, type="image/png")],
            category="image",
            creators=[NFTCreator(address=creator_address, share=100, verified=True)]
        )
        
        # Create collection if specified
        collection = None
        if self.collection_name:
            collection = NFTCollection(
                name=self.collection_name,
                family=self.collection_family
            )
        
        return NFTMetadata(
            name=self.name,
            symbol=self.symbol,
            description=self.description,
            image=self.image_url,
            external_url=self.external_url,
            animation_url=self.animation_url,
            attributes=attributes,
            properties=properties,
            collection=collection,
            seller_fee_basis_points=self.seller_fee_basis_points
        )

@dataclass
class NFTResponse:
    """NFT creation response model"""
    success: bool
    message: str
    nft_mint_address: Optional[str] = None
    metadata_pda: Optional[str] = None
    metadata_uri: Optional[str] = None
    associated_token_account: Optional[str] = None
    transaction_signature: Optional[str] = None
    explorer_url: Optional[str] = None
    nft_details: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON response"""
        result = {
            "success": self.success,
            "message": self.message
        }
        
        if self.nft_mint_address:
            result["nft_mint_address"] = self.nft_mint_address
        if self.metadata_pda:
            result["metadata_pda"] = self.metadata_pda
        if self.metadata_uri:
            result["metadata_uri"] = self.metadata_uri
        if self.associated_token_account:
            result["associated_token_account"] = self.associated_token_account
        if self.transaction_signature:
            result["transaction_signature"] = self.transaction_signature
        if self.explorer_url:
            result["explorer_url"] = self.explorer_url
        if self.nft_details:
            result["nft_details"] = self.nft_details
        if self.created_at:
            result["created_at"] = self.created_at.isoformat()
            
        return result
