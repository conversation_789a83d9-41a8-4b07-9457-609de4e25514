# src/models/token.py
from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from datetime import datetime

@dataclass
class TokenRequest:
    """Request model for token creation"""
    name: str
    symbol: str
    amount: int = 1
    metadata_uri: Optional[str] = None
    description: Optional[str] = None
    image_uri: Optional[str] = None
    attributes: Optional[List[Dict[str, Any]]] = None
    seller_fee_basis_points: int = 0
    is_mutable: bool = True
    
    def validate(self) -> List[str]:
        """Validate token request data"""
        errors = []
        
        if not self.name or len(self.name.strip()) == 0:
            errors.append("Token name is required")
        elif len(self.name) > 32:
            errors.append("Token name must be 32 characters or less")
            
        if not self.symbol or len(self.symbol.strip()) == 0:
            errors.append("Token symbol is required")
        elif len(self.symbol) > 10:
            errors.append("Token symbol must be 10 characters or less")
            
        if self.amount <= 0:
            errors.append("Amount must be greater than 0")
        elif self.amount > 1000000:
            errors.append("Amount cannot exceed 1,000,000")
            
        if self.seller_fee_basis_points < 0 or self.seller_fee_basis_points > 10000:
            errors.append("Seller fee basis points must be between 0 and 10000")
            
        return errors

@dataclass
class TokenMetadata:
    """Token metadata structure"""
    name: str
    symbol: str
    description: Optional[str] = None
    image: Optional[str] = None
    animation_url: Optional[str] = None
    external_url: Optional[str] = None
    attributes: Optional[List[Dict[str, Any]]] = None
    properties: Optional[Dict[str, Any]] = None
    seller_fee_basis_points: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        result = {
            "name": self.name,
            "symbol": self.symbol,
            "seller_fee_basis_points": self.seller_fee_basis_points
        }
        
        if self.description:
            result["description"] = self.description
        if self.image:
            result["image"] = self.image
        if self.animation_url:
            result["animation_url"] = self.animation_url
        if self.external_url:
            result["external_url"] = self.external_url
        if self.attributes:
            result["attributes"] = self.attributes
        if self.properties:
            result["properties"] = self.properties
            
        return result

@dataclass
class TokenResponse:
    """Response model for token creation"""
    success: bool
    message: str
    token_mint_address: Optional[str] = None
    associated_token_account: Optional[str] = None
    metadata_pda: Optional[str] = None
    transaction_signature: Optional[str] = None
    explorer_url: Optional[str] = None
    token_details: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON response"""
        result = {
            "success": self.success,
            "message": self.message
        }
        
        if self.token_mint_address:
            result["token_mint_address"] = self.token_mint_address
        if self.associated_token_account:
            result["associated_token_account"] = self.associated_token_account
        if self.metadata_pda:
            result["metadata_pda"] = self.metadata_pda
        if self.transaction_signature:
            result["transaction_signature"] = self.transaction_signature
        if self.explorer_url:
            result["explorer_url"] = self.explorer_url
        if self.token_details:
            result["token_details"] = self.token_details
        if self.created_at:
            result["created_at"] = self.created_at.isoformat()
            
        return result
