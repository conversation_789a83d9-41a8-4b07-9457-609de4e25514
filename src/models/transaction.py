# src/models/transaction.py
from dataclasses import dataclass
from enum import Enum
from typing import Optional, Dict, Any
from datetime import datetime

class TransactionStatus(Enum):
    """Transaction status enumeration"""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    FAILED = "failed"
    FINALIZED = "finalized"

@dataclass
class TransactionResult:
    """Transaction result model"""
    signature: str
    status: TransactionStatus
    block_height: Optional[int] = None
    slot: Optional[int] = None
    confirmation_status: Optional[str] = None
    err: Optional[Dict[str, Any]] = None
    logs: Optional[list] = None
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = {
            "signature": self.signature,
            "status": self.status.value
        }
        
        if self.block_height:
            result["block_height"] = self.block_height
        if self.slot:
            result["slot"] = self.slot
        if self.confirmation_status:
            result["confirmation_status"] = self.confirmation_status
        if self.err:
            result["error"] = self.err
        if self.logs:
            result["logs"] = self.logs
        if self.created_at:
            result["created_at"] = self.created_at.isoformat()
            
        return result
    
    @property
    def is_successful(self) -> bool:
        """Check if transaction was successful"""
        return self.status in [TransactionStatus.CONFIRMED, TransactionStatus.FINALIZED] and not self.err
    
    @property
    def is_failed(self) -> bool:
        """Check if transaction failed"""
        return self.status == TransactionStatus.FAILED or self.err is not None
