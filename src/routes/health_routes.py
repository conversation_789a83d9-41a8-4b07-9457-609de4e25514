# src/routes/health_routes.py
from flask import Blueprint, jsonify, current_app
from ..utils.logger import get_logger

health_bp = Blueprint('health', __name__)
logger = get_logger(__name__)

@health_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Basic health check
        return jsonify({
            "status": "healthy",
            "message": "Solana NFT Creator API is running",
            "version": "1.0.0",
            "network": current_app.config.get('SOLANA_NETWORK', 'devnet')
        }), 200
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return jsonify({
            "status": "unhealthy",
            "message": "Health check failed",
            "error": str(e)
        }), 500

@health_bp.route('/status', methods=['GET'])
def status_check():
    """Detailed status check"""
    try:
        from ..services import WalletService, SolanaService
        
        # Check wallet
        wallet_service = WalletService(current_app.config['SOLANA_WALLET_FILE_PATH'])
        wallet_info = wallet_service.get_wallet_info()
        
        # Check Solana connection
        solana_service = SolanaService(
            current_app.config['SOLANA_RPC_URL'],
            current_app.config['SOLANA_NETWORK']
        )
        
        try:
            balance = solana_service.get_balance(wallet_service.get_public_key())
            solana_status = "connected"
        except Exception as e:
            balance = None
            solana_status = f"error: {str(e)}"
        
        return jsonify({
            "status": "healthy",
            "wallet": wallet_info,
            "solana": {
                "status": solana_status,
                "network": current_app.config['SOLANA_NETWORK'],
                "rpc_url": current_app.config['SOLANA_RPC_URL'],
                "balance_sol": balance
            },
            "config": {
                "debug": current_app.debug,
                "testing": current_app.testing
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Status check failed: {str(e)}")
        return jsonify({
            "status": "unhealthy",
            "error": str(e)
        }), 500
