# src/routes/marketplace_routes.py
from flask import Blueprint, request, jsonify, current_app
from ..services import SolanaService, WalletService
from ..services.auction_house_service import AuctionHouseService
from ..models.auction_house import ListingRequest, BidRequest, PurchaseRequest
from ..utils.exceptions import ValidationError, SolanaAPIError
from ..utils.logger import get_logger

marketplace_bp = Blueprint('marketplace', __name__)
logger = get_logger(__name__)

def get_marketplace_services():
    """Get marketplace service instances"""
    wallet_service = WalletService(current_app.config['SOLANA_WALLET_FILE_PATH'])
    solana_service = SolanaService(
        current_app.config['SOLANA_RPC_URL'],
        current_app.config['SOLANA_NETWORK']
    )
    auction_house_service = AuctionHouseService(solana_service, wallet_service)
    return auction_house_service, solana_service, wallet_service

@marketplace_bp.route('/auction_house/create', methods=['POST'])
def create_auction_house():
    """Create a new Auction House"""
    logger.info("Received request to create Auction House")
    
    try:
        data = request.get_json() if request.is_json else {}
        
        auction_house_service, _, _ = get_marketplace_services()
        
        # Extract parameters
        seller_fee_basis_points = data.get('seller_fee_basis_points', 250)  # 2.5% default
        requires_sign_off = data.get('requires_sign_off', False)
        can_change_sale_price = data.get('can_change_sale_price', True)
        
        # Create auction house
        response = auction_house_service.create_auction_house(
            seller_fee_basis_points=seller_fee_basis_points,
            requires_sign_off=requires_sign_off,
            can_change_sale_price=can_change_sale_price
        )
        
        if response.success:
            logger.info(f"Auction House created: {response.auction_house_address}")
            return jsonify(response.to_dict()), 201
        else:
            logger.error(f"Auction House creation failed: {response.message}")
            return jsonify(response.to_dict()), 500
            
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return jsonify({
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }), 500

@marketplace_bp.route('/auction_house/info', methods=['GET'])
def get_auction_house_info():
    """Get Auction House information"""
    try:
        auction_house_service, _, _ = get_marketplace_services()
        config = auction_house_service.get_auction_house_info()
        
        if config:
            return jsonify({
                "success": True,
                "auction_house": config.to_dict()
            }), 200
        else:
            return jsonify({
                "success": False,
                "message": "No Auction House configured"
            }), 404
            
    except Exception as e:
        logger.error(f"Error getting auction house info: {str(e)}")
        return jsonify({"error": str(e)}), 500

@marketplace_bp.route('/list', methods=['POST'])
def list_nft():
    """List an NFT for sale"""
    logger.info("Received request to list NFT")
    
    try:
        if not request.is_json:
            return jsonify({"error": "Content-Type must be application/json"}), 400
        
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400
        
        # Create listing request
        listing_request = ListingRequest(
            nft_mint=data.get('nft_mint', '').strip(),
            price=float(data.get('price', 0)),
            duration_hours=data.get('duration_hours'),
            currency_mint=data.get('currency_mint', 'So11111111111111111111111111111111111111112')
        )
        
        auction_house_service, _, _ = get_marketplace_services()
        
        # List NFT
        response = auction_house_service.list_nft(listing_request)
        
        if response.success:
            logger.info(f"NFT listed successfully")
            return jsonify(response.to_dict()), 201
        else:
            logger.error(f"NFT listing failed: {response.message}")
            return jsonify(response.to_dict()), 500
            
    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        return jsonify({"error": str(e)}), 400
        
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return jsonify({
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }), 500

@marketplace_bp.route('/bid', methods=['POST'])
def place_bid():
    """Place a bid on an NFT"""
    logger.info("Received request to place bid")
    
    try:
        if not request.is_json:
            return jsonify({"error": "Content-Type must be application/json"}), 400
        
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400
        
        # Create bid request
        bid_request = BidRequest(
            nft_mint=data.get('nft_mint', '').strip(),
            price=float(data.get('price', 0)),
            duration_hours=data.get('duration_hours'),
            currency_mint=data.get('currency_mint', 'So11111111111111111111111111111111111111112')
        )
        
        auction_house_service, _, _ = get_marketplace_services()
        
        # Place bid
        response = auction_house_service.place_bid(bid_request)
        
        if response.success:
            logger.info(f"Bid placed successfully")
            return jsonify(response.to_dict()), 201
        else:
            logger.error(f"Bid placement failed: {response.message}")
            return jsonify(response.to_dict()), 500
            
    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        return jsonify({"error": str(e)}), 400
        
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return jsonify({
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }), 500

@marketplace_bp.route('/purchase', methods=['POST'])
def purchase_nft():
    """Purchase an NFT directly"""
    logger.info("Received request to purchase NFT")
    
    try:
        if not request.is_json:
            return jsonify({"error": "Content-Type must be application/json"}), 400
        
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400
        
        # Create purchase request
        purchase_request = PurchaseRequest(
            nft_mint=data.get('nft_mint', '').strip(),
            max_price=float(data.get('max_price', 0)),
            currency_mint=data.get('currency_mint', 'So11111111111111111111111111111111111111112')
        )
        
        auction_house_service, _, _ = get_marketplace_services()
        
        # Purchase NFT
        response = auction_house_service.purchase_nft(purchase_request)
        
        if response.success:
            logger.info(f"NFT purchased successfully")
            return jsonify(response.to_dict()), 200
        else:
            logger.error(f"NFT purchase failed: {response.message}")
            return jsonify(response.to_dict()), 500
            
    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        return jsonify({"error": str(e)}), 400
        
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return jsonify({
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }), 500

@marketplace_bp.route('/bid/<bid_id>/accept', methods=['POST'])
def accept_bid(bid_id):
    """Accept a bid"""
    logger.info(f"Received request to accept bid: {bid_id}")
    
    try:
        auction_house_service, _, _ = get_marketplace_services()
        
        # Accept bid
        response = auction_house_service.accept_bid(bid_id)
        
        if response.success:
            logger.info(f"Bid accepted successfully: {bid_id}")
            return jsonify(response.to_dict()), 200
        else:
            logger.error(f"Bid acceptance failed: {response.message}")
            return jsonify(response.to_dict()), 500
            
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return jsonify({
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }), 500

@marketplace_bp.route('/listing/<listing_id>/cancel', methods=['POST'])
def cancel_listing(listing_id):
    """Cancel a listing"""
    logger.info(f"Received request to cancel listing: {listing_id}")
    
    try:
        auction_house_service, _, _ = get_marketplace_services()
        
        # Cancel listing
        response = auction_house_service.cancel_listing(listing_id)
        
        if response.success:
            logger.info(f"Listing cancelled successfully: {listing_id}")
            return jsonify(response.to_dict()), 200
        else:
            logger.error(f"Listing cancellation failed: {response.message}")
            return jsonify(response.to_dict()), 500
            
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return jsonify({
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }), 500

@marketplace_bp.route('/bid/<bid_id>/cancel', methods=['POST'])
def cancel_bid(bid_id):
    """Cancel a bid"""
    logger.info(f"Received request to cancel bid: {bid_id}")
    
    try:
        auction_house_service, _, _ = get_marketplace_services()
        
        # Cancel bid
        response = auction_house_service.cancel_bid(bid_id)
        
        if response.success:
            logger.info(f"Bid cancelled successfully: {bid_id}")
            return jsonify(response.to_dict()), 200
        else:
            logger.error(f"Bid cancellation failed: {response.message}")
            return jsonify(response.to_dict()), 500
            
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return jsonify({
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }), 500

@marketplace_bp.route('/listings', methods=['GET'])
def get_listings():
    """Get all active listings"""
    try:
        nft_mint = request.args.get('nft_mint')

        auction_house_service, _, _ = get_marketplace_services()
        listings = auction_house_service.get_listings(nft_mint)

        return jsonify({
            "success": True,
            "listings": [listing.to_dict() for listing in listings],
            "count": len(listings)
        }), 200

    except Exception as e:
        logger.error(f"Error getting listings: {str(e)}")
        return jsonify({"error": str(e)}), 500

@marketplace_bp.route('/bids', methods=['GET'])
def get_bids():
    """Get all active bids"""
    try:
        nft_mint = request.args.get('nft_mint')

        auction_house_service, _, _ = get_marketplace_services()
        bids = auction_house_service.get_bids(nft_mint)

        return jsonify({
            "success": True,
            "bids": [bid.to_dict() for bid in bids],
            "count": len(bids)
        }), 200

    except Exception as e:
        logger.error(f"Error getting bids: {str(e)}")
        return jsonify({"error": str(e)}), 500

@marketplace_bp.route('/sales', methods=['GET'])
def get_sales():
    """Get all sales"""
    try:
        auction_house_service, _, _ = get_marketplace_services()
        sales = auction_house_service.get_sales()

        return jsonify({
            "success": True,
            "sales": [sale.to_dict() for sale in sales],
            "count": len(sales)
        }), 200

    except Exception as e:
        logger.error(f"Error getting sales: {str(e)}")
        return jsonify({"error": str(e)}), 500

@marketplace_bp.route('/stats', methods=['GET'])
def get_marketplace_stats():
    """Get marketplace statistics"""
    try:
        auction_house_service, _, _ = get_marketplace_services()

        listings = auction_house_service.get_listings()
        bids = auction_house_service.get_bids()
        sales = auction_house_service.get_sales()

        # Calculate stats
        total_volume = sum(sale.price for sale in sales)
        avg_price = total_volume / len(sales) if sales else 0

        stats = {
            "active_listings": len(listings),
            "active_bids": len(bids),
            "total_sales": len(sales),
            "total_volume_sol": total_volume,
            "average_price_sol": avg_price,
            "auction_house_configured": auction_house_service.get_auction_house_info() is not None
        }

        return jsonify({
            "success": True,
            "stats": stats
        }), 200

    except Exception as e:
        logger.error(f"Error getting marketplace stats: {str(e)}")
        return jsonify({"error": str(e)}), 500

@marketplace_bp.route('/examples', methods=['GET'])
def get_marketplace_examples():
    """Get marketplace API usage examples"""
    examples = {
        "create_auction_house": {
            "method": "POST",
            "endpoint": "/api/v1/marketplace/auction_house/create",
            "body": {
                "seller_fee_basis_points": 250,
                "requires_sign_off": False,
                "can_change_sale_price": True
            }
        },
        "list_nft": {
            "method": "POST",
            "endpoint": "/api/v1/marketplace/list",
            "body": {
                "nft_mint": "YOUR_NFT_MINT_ADDRESS",
                "price": 1.5,
                "duration_hours": 24
            }
        },
        "place_bid": {
            "method": "POST",
            "endpoint": "/api/v1/marketplace/bid",
            "body": {
                "nft_mint": "YOUR_NFT_MINT_ADDRESS",
                "price": 1.2,
                "duration_hours": 12
            }
        },
        "purchase_nft": {
            "method": "POST",
            "endpoint": "/api/v1/marketplace/purchase",
            "body": {
                "nft_mint": "YOUR_NFT_MINT_ADDRESS",
                "max_price": 2.0
            }
        },
        "management": {
            "accept_bid": "POST /api/v1/marketplace/bid/{bid_id}/accept",
            "cancel_listing": "POST /api/v1/marketplace/listing/{listing_id}/cancel",
            "cancel_bid": "POST /api/v1/marketplace/bid/{bid_id}/cancel"
        },
        "data_endpoints": {
            "listings": "GET /api/v1/marketplace/listings",
            "bids": "GET /api/v1/marketplace/bids",
            "sales": "GET /api/v1/marketplace/sales",
            "stats": "GET /api/v1/marketplace/stats",
            "auction_house_info": "GET /api/v1/marketplace/auction_house/info"
        }
    }

    return jsonify(examples), 200
