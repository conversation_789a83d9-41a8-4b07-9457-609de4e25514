# src/routes/nft_routes.py
from flask import Blueprint, request, jsonify, current_app
from ..services import NFTService, SolanaService, WalletService
from ..utils.exceptions import ValidationError, SolanaAPIError
from ..utils.logger import get_logger

nft_bp = Blueprint('nft', __name__)
logger = get_logger(__name__)

def get_nft_services():
    """Get NFT service instances"""
    wallet_service = WalletService(current_app.config['SOLANA_WALLET_FILE_PATH'])
    solana_service = SolanaService(
        current_app.config['SOLANA_RPC_URL'],
        current_app.config['SOLANA_NETWORK']
    )
    nft_service = NFTService(solana_service, wallet_service)
    return nft_service, solana_service, wallet_service

@nft_bp.route('/create_nft', methods=['POST'])
def create_nft():
    """Create a new NFT (simple NFT-like token with metadata)"""
    logger.info("Received request to create NFT")

    try:
        # Validate request data
        if not request.is_json:
            return jsonify({"error": "Content-Type must be application/json"}), 400

        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400

        # Get services
        from ..services.simple_nft_service import SimpleNFTService
        _, solana_service, wallet_service = get_nft_services()
        simple_nft_service = SimpleNFTService(solana_service, wallet_service)

        # Validate NFT request
        try:
            nft_request = simple_nft_service.validate_nft_request(data)
        except ValidationError as e:
            logger.warning(f"Validation error: {str(e)}")
            return jsonify({"error": str(e)}), 400

        logger.info(f"Creating NFT: {nft_request.name} ({nft_request.symbol})")

        # Create NFT
        response = simple_nft_service.create_simple_nft(nft_request)

        if response.success:
            logger.info(f"NFT created successfully: {response.nft_mint_address}")
            return jsonify(response.to_dict()), 201
        else:
            logger.error(f"NFT creation failed: {response.message}")
            return jsonify(response.to_dict()), 500

    except SolanaAPIError as e:
        logger.error(f"Solana API error: {str(e)}")
        return jsonify({
            "error": "Solana API error",
            "message": str(e),
            "error_code": e.error_code,
            "details": e.details
        }), 500

    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return jsonify({
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }), 500

@nft_bp.route('/nft/<mint_address>', methods=['GET'])
def get_nft_info(mint_address):
    """Get NFT information"""
    try:
        nft_service, _, _ = get_nft_services()
        nft_info = nft_service.get_nft_info(mint_address)
        
        if "error" in nft_info:
            return jsonify(nft_info), 404
        
        return jsonify(nft_info), 200
        
    except Exception as e:
        logger.error(f"Error getting NFT info: {str(e)}")
        return jsonify({"error": str(e)}), 500

@nft_bp.route('/nft/<mint_address>/owner/<owner_address>', methods=['GET'])
def get_nft_balance(mint_address, owner_address):
    """Get NFT balance for an owner"""
    try:
        nft_service, _, _ = get_nft_services()
        balance_info = nft_service.get_nft_balance(owner_address, mint_address)
        
        if "error" in balance_info:
            return jsonify(balance_info), 400
        
        return jsonify(balance_info), 200
        
    except Exception as e:
        logger.error(f"Error getting NFT balance: {str(e)}")
        return jsonify({"error": str(e)}), 500

@nft_bp.route('/create_nft_simple', methods=['POST'])
def create_nft_simple():
    """Create a simple NFT-like token (SPL token with 0 decimals and supply of 1)"""
    logger.info("Received request to create simple NFT-like token")

    try:
        # Validate request data
        if not request.is_json:
            return jsonify({"error": "Content-Type must be application/json"}), 400

        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400

        # Extract required fields
        name = data.get('name')
        symbol = data.get('symbol')

        if not all([name, symbol]):
            return jsonify({
                "error": "Missing required fields",
                "required": ["name", "symbol"]
            }), 400

        # Import token service for simple token creation
        from ..services import TokenService

        # Get services
        _, solana_service, wallet_service = get_nft_services()
        token_service = TokenService(solana_service, wallet_service)

        # Create simple token request (NFT-like: 0 decimals, supply of 1)
        from ..models.token import TokenRequest
        token_request = TokenRequest(
            name=name,
            symbol=symbol,
            amount=1  # NFT-like: supply of 1
        )

        logger.info(f"Creating simple NFT-like token: {token_request.name}")

        # Create token
        response = token_service.create_token(token_request)

        if response.success:
            logger.info(f"Simple NFT-like token created successfully: {response.token_mint_address}")

            # Modify response to indicate it's NFT-like
            result = response.to_dict()
            result["message"] = "NFT-like token created successfully! (SPL token with 0 decimals and supply of 1)"
            result["nft_like"] = True
            result["metadata_note"] = "This is a simple NFT-like token. For full NFT with metadata, use /create_nft endpoint"

            return jsonify(result), 201
        else:
            logger.error(f"Simple NFT-like token creation failed: {response.message}")
            return jsonify(response.to_dict()), 500

    except Exception as e:
        logger.error(f"Unexpected error in simple NFT-like token creation: {str(e)}", exc_info=True)
        return jsonify({
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }), 500

@nft_bp.route('/nft_examples', methods=['GET'])
def get_nft_examples():
    """Get example NFT creation requests"""
    examples = {
        "simple_nft": {
            "name": "My First NFT",
            "symbol": "FIRST",
            "description": "This is my first NFT on Solana",
            "image_url": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png"
        },
        "detailed_nft": {
            "name": "Detailed NFT",
            "symbol": "DETAIL",
            "description": "A detailed NFT with attributes and collection",
            "image_url": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png",
            "external_url": "https://myproject.com",
            "attributes": [
                {"trait_type": "Color", "value": "Blue"},
                {"trait_type": "Rarity", "value": "Rare"},
                {"trait_type": "Level", "value": "10", "display_type": "number"}
            ],
            "collection_name": "My Collection",
            "collection_family": "My NFT Series",
            "seller_fee_basis_points": 500,
            "is_mutable": True,
            "max_supply": 1000
        },
        "endpoints": {
            "create_simple": "POST /api/v1/create_nft_simple",
            "create_detailed": "POST /api/v1/create_nft",
            "get_info": "GET /api/v1/nft/{mint_address}",
            "get_balance": "GET /api/v1/nft/{mint_address}/owner/{owner_address}",
            "list_nfts": "GET /api/v1/nfts"
        },
        "note": "These are simple NFT-like tokens (SPL tokens with 0 decimals and supply of 1) with metadata storage"
    }

    return jsonify(examples), 200

@nft_bp.route('/nfts', methods=['GET'])
def list_nfts():
    """List all created NFTs"""
    try:
        from ..services.simple_nft_service import SimpleNFTService
        _, solana_service, wallet_service = get_nft_services()
        simple_nft_service = SimpleNFTService(solana_service, wallet_service)

        nfts_info = simple_nft_service.list_created_nfts()
        return jsonify(nfts_info), 200

    except Exception as e:
        logger.error(f"Error listing NFTs: {str(e)}")
        return jsonify({"error": str(e)}), 500
