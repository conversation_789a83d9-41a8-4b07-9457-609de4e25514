# src/routes/token_routes.py
from flask import Blueprint, request, jsonify, current_app
from ..services import TokenService, SolanaService, WalletService
from ..utils.validators import validate_token_request
from ..utils.exceptions import ValidationError, SolanaAPIError
from ..utils.logger import get_logger

token_bp = Blueprint('token', __name__)
logger = get_logger(__name__)

def get_services():
    """Get service instances"""
    wallet_service = WalletService(current_app.config['SOLANA_WALLET_FILE_PATH'])
    solana_service = SolanaService(
        current_app.config['SOLANA_RPC_URL'],
        current_app.config['SOLANA_NETWORK']
    )
    token_service = TokenService(solana_service, wallet_service)
    return token_service, solana_service, wallet_service

@token_bp.route('/create_token', methods=['POST'])
def create_token():
    """Create a new SPL token"""
    logger.info("Received request to create token")
    
    try:
        # Validate request data
        if not request.is_json:
            return jsonify({"error": "Content-Type must be application/json"}), 400
        
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400
        
        # Validate token request
        try:
            token_request = validate_token_request(data)
        except ValidationError as e:
            logger.warning(f"Validation error: {str(e)}")
            return jsonify({"error": str(e)}), 400
        
        logger.info(f"Creating token: {token_request.name} ({token_request.symbol})")
        
        # Get services
        token_service, _, _ = get_services()
        
        # Create token
        response = token_service.create_token(token_request)
        
        if response.success:
            logger.info(f"Token created successfully: {response.token_mint_address}")
            return jsonify(response.to_dict()), 201
        else:
            logger.error(f"Token creation failed: {response.message}")
            return jsonify(response.to_dict()), 500
            
    except SolanaAPIError as e:
        logger.error(f"Solana API error: {str(e)}")
        return jsonify({
            "error": "Solana API error",
            "message": str(e),
            "error_code": e.error_code,
            "details": e.details
        }), 500
        
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return jsonify({
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }), 500

@token_bp.route('/token/<mint_address>', methods=['GET'])
def get_token_info(mint_address):
    """Get token information"""
    try:
        token_service, _, _ = get_services()
        token_info = token_service.get_token_info(mint_address)
        
        if "error" in token_info:
            return jsonify(token_info), 404
        
        return jsonify(token_info), 200
        
    except Exception as e:
        logger.error(f"Error getting token info: {str(e)}")
        return jsonify({"error": str(e)}), 500

@token_bp.route('/balance/<owner_address>/<mint_address>', methods=['GET'])
def get_token_balance(owner_address, mint_address):
    """Get token balance for an owner"""
    try:
        token_service, _, _ = get_services()
        balance_info = token_service.get_token_balance(owner_address, mint_address)
        
        if "error" in balance_info:
            return jsonify(balance_info), 400
        
        return jsonify(balance_info), 200
        
    except Exception as e:
        logger.error(f"Error getting token balance: {str(e)}")
        return jsonify({"error": str(e)}), 500

@token_bp.route('/wallet/info', methods=['GET'])
def get_wallet_info():
    """Get wallet information"""
    try:
        _, _, wallet_service = get_services()
        wallet_info = wallet_service.get_wallet_info()
        return jsonify(wallet_info), 200
        
    except Exception as e:
        logger.error(f"Error getting wallet info: {str(e)}")
        return jsonify({"error": str(e)}), 500

@token_bp.route('/wallet/balance', methods=['GET'])
def get_wallet_balance():
    """Get wallet SOL balance"""
    try:
        _, solana_service, wallet_service = get_services()
        
        public_key = wallet_service.get_public_key()
        balance = solana_service.get_balance(public_key)
        
        return jsonify({
            "public_key": str(public_key),
            "balance_sol": balance,
            "balance_lamports": int(balance * 1_000_000_000)
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting wallet balance: {str(e)}")
        return jsonify({"error": str(e)}), 500
