# src/services/auction_house_service.py
import struct
import uuid
from typing import Optional, List, Tuple
from datetime import datetime, timedelta
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solders.instruction import Instruction, AccountMeta
from ..models.auction_house import (
    AuctionHouseConfig, AuctionHouseResponse, NFTListing, NFTBid, NFTSale,
    ListingRequest, BidRequest, PurchaseRequest, ListingStatus, BidStatus, SaleStatus
)
from .solana_service import SolanaService
from .wallet_service import WalletService
from ..utils.logger import get_logger
from ..utils.helpers import generate_explorer_url
from ..utils.exceptions import SolanaAPIError, ValidationError

class AuctionHouseService:
    """Service for Metaplex Auction House operations"""
    
    def __init__(self, solana_service: SolanaService, wallet_service: WalletService):
        self.solana_service = solana_service
        self.wallet_service = wallet_service
        self.logger = get_logger(__name__)
        
        # Metaplex Auction House Program ID
        self.AUCTION_HOUSE_PROGRAM_ID = Pubkey.from_string('hausS13jsjafwWwGqZTUQRmWyvyxn9EQpqMaoBaNEKo')
        
        # Token Program ID
        self.TOKEN_PROGRAM_ID = Pubkey.from_string('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA')
        
        # Associated Token Program ID
        self.ASSOCIATED_TOKEN_PROGRAM_ID = Pubkey.from_string('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL')
        
        # System Program ID
        self.SYSTEM_PROGRAM_ID = Pubkey.from_string('********************************')
        
        # SOL mint (native SOL)
        self.SOL_MINT = Pubkey.from_string('So111111111********************************')
        
        # File-based storage for persistence
        self.data_dir = "marketplace_data"
        self._ensure_data_dir()
        self.listings: List[NFTListing] = self._load_listings()
        self.bids: List[NFTBid] = self._load_bids()
        self.sales: List[NFTSale] = self._load_sales()
        self.auction_house_config: Optional[AuctionHouseConfig] = self._load_auction_house_config()
    
    def get_auction_house_pda(self, authority: Pubkey, treasury_mint: Pubkey) -> Tuple[Pubkey, int]:
        """Get Auction House Program Derived Address"""
        seeds = [
            b"auction_house",
            bytes(authority),
            bytes(treasury_mint)
        ]
        return Pubkey.find_program_address(seeds, self.AUCTION_HOUSE_PROGRAM_ID)
    
    def get_auction_house_fee_account_pda(self, auction_house: Pubkey) -> Tuple[Pubkey, int]:
        """Get Auction House fee account PDA"""
        seeds = [
            b"auction_house",
            bytes(auction_house),
            b"fee_payer"
        ]
        return Pubkey.find_program_address(seeds, self.AUCTION_HOUSE_PROGRAM_ID)
    
    def get_auction_house_treasury_pda(self, auction_house: Pubkey) -> Tuple[Pubkey, int]:
        """Get Auction House treasury PDA"""
        seeds = [
            b"auction_house",
            bytes(auction_house),
            b"treasury"
        ]
        return Pubkey.find_program_address(seeds, self.AUCTION_HOUSE_PROGRAM_ID)
    
    def create_auction_house(
        self,
        seller_fee_basis_points: int = 250,  # 2.5% fee
        requires_sign_off: bool = False,
        can_change_sale_price: bool = True
    ) -> AuctionHouseResponse:
        """Create a new Auction House"""
        try:
            self.logger.info("Creating new Auction House")
            
            # Load authority wallet
            authority_keypair = self.wallet_service.load_keypair()
            authority_pubkey = authority_keypair.pubkey()
            
            # Use SOL as treasury mint
            treasury_mint = self.SOL_MINT
            
            # Get Auction House PDA
            auction_house_pda, auction_house_bump = self.get_auction_house_pda(
                authority_pubkey, treasury_mint
            )
            
            # Get fee and treasury PDAs
            fee_payer_pda, fee_payer_bump = self.get_auction_house_fee_account_pda(auction_house_pda)
            treasury_pda, treasury_bump = self.get_auction_house_treasury_pda(auction_house_pda)
            
            # For now, we'll create a simple configuration without actual on-chain creation
            # In production, you'd create the actual Auction House instruction
            
            config = AuctionHouseConfig(
                authority=str(authority_pubkey),
                treasury_mint=str(treasury_mint),
                fee_withdrawal_destination=str(authority_pubkey),
                treasury_withdrawal_destination=str(authority_pubkey),
                seller_fee_basis_points=seller_fee_basis_points,
                requires_sign_off=requires_sign_off,
                can_change_sale_price=can_change_sale_price
            )
            
            self.auction_house_config = config
            self._save_auction_house_config()

            self.logger.info(f"Auction House configured: {auction_house_pda}")

            return AuctionHouseResponse(
                success=True,
                message="Auction House created successfully!",
                auction_house_address=str(auction_house_pda),
                data=config.to_dict()
            )
            
        except Exception as e:
            error_msg = f"Failed to create Auction House: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return AuctionHouseResponse(
                success=False,
                message=error_msg
            )
    
    def list_nft(self, listing_request: ListingRequest) -> AuctionHouseResponse:
        """List an NFT for sale"""
        try:
            self.logger.info(f"Listing NFT: {listing_request.nft_mint} for {listing_request.price} SOL")
            
            # Validate request
            errors = listing_request.validate()
            if errors:
                raise ValidationError(f"Validation failed: {'; '.join(errors)}")
            
            # Load seller wallet
            seller_keypair = self.wallet_service.load_keypair()
            seller_pubkey = seller_keypair.pubkey()
            
            # Check if seller owns the NFT
            nft_mint = Pubkey.from_string(listing_request.nft_mint)
            ata_address = self.solana_service.get_associated_token_address(seller_pubkey, nft_mint)
            
            # Check token account exists and has balance
            account_info = self.solana_service.client.get_account_info(ata_address)
            if account_info.value is None:
                raise ValidationError("Seller does not own this NFT")
            
            # Calculate expiration
            expires_at = None
            if listing_request.duration_hours:
                expires_at = datetime.utcnow() + timedelta(hours=listing_request.duration_hours)
            
            # Create listing
            listing_id = str(uuid.uuid4())
            listing = NFTListing(
                listing_id=listing_id,
                nft_mint=listing_request.nft_mint,
                seller=str(seller_pubkey),
                price=listing_request.price,
                currency_mint=listing_request.currency_mint,
                expires_at=expires_at,
                auction_house=str(self.auction_house_config.authority) if self.auction_house_config else None
            )
            
            # Store listing
            self.listings.append(listing)
            self._save_listings()

            self.logger.info(f"NFT listed successfully: {listing_id}")

            return AuctionHouseResponse(
                success=True,
                message="NFT listed successfully!",
                data=listing.to_dict()
            )
            
        except Exception as e:
            error_msg = f"Failed to list NFT: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return AuctionHouseResponse(
                success=False,
                message=error_msg
            )
    
    def place_bid(self, bid_request: BidRequest) -> AuctionHouseResponse:
        """Place a bid on an NFT"""
        try:
            self.logger.info(f"Placing bid on NFT: {bid_request.nft_mint} for {bid_request.price} SOL")
            
            # Validate request
            errors = bid_request.validate()
            if errors:
                raise ValidationError(f"Validation failed: {'; '.join(errors)}")
            
            # Load bidder wallet
            bidder_keypair = self.wallet_service.load_keypair()
            bidder_pubkey = bidder_keypair.pubkey()
            
            # Check bidder has sufficient balance
            balance = self.solana_service.get_balance(bidder_pubkey)
            required_lamports = int(bid_request.price * 1_000_000_000)  # Convert SOL to lamports
            
            if balance < required_lamports:
                raise ValidationError(f"Insufficient balance. Required: {bid_request.price} SOL, Available: {balance / 1_000_000_000} SOL")
            
            # Calculate expiration
            expires_at = None
            if bid_request.duration_hours:
                expires_at = datetime.utcnow() + timedelta(hours=bid_request.duration_hours)
            
            # Create bid
            bid_id = str(uuid.uuid4())
            bid = NFTBid(
                bid_id=bid_id,
                nft_mint=bid_request.nft_mint,
                bidder=str(bidder_pubkey),
                price=bid_request.price,
                currency_mint=bid_request.currency_mint,
                expires_at=expires_at,
                auction_house=str(self.auction_house_config.authority) if self.auction_house_config else None
            )
            
            # Store bid
            self.bids.append(bid)
            self._save_bids()

            self.logger.info(f"Bid placed successfully: {bid_id}")

            return AuctionHouseResponse(
                success=True,
                message="Bid placed successfully!",
                data=bid.to_dict()
            )
            
        except Exception as e:
            error_msg = f"Failed to place bid: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return AuctionHouseResponse(
                success=False,
                message=error_msg
            )
    
    def get_listings(self, nft_mint: Optional[str] = None) -> List[NFTListing]:
        """Get active listings"""
        active_listings = [l for l in self.listings if l.status == ListingStatus.ACTIVE]
        
        if nft_mint:
            active_listings = [l for l in active_listings if l.nft_mint == nft_mint]
        
        # Filter out expired listings
        now = datetime.utcnow()
        valid_listings = []
        for listing in active_listings:
            if listing.expires_at is None or listing.expires_at > now:
                valid_listings.append(listing)
            else:
                # Mark as expired
                listing.status = ListingStatus.EXPIRED
                listing.updated_at = now
        
        return valid_listings
    
    def get_bids(self, nft_mint: Optional[str] = None) -> List[NFTBid]:
        """Get active bids"""
        active_bids = [b for b in self.bids if b.status == BidStatus.ACTIVE]
        
        if nft_mint:
            active_bids = [b for b in active_bids if b.nft_mint == nft_mint]
        
        # Filter out expired bids
        now = datetime.utcnow()
        valid_bids = []
        for bid in active_bids:
            if bid.expires_at is None or bid.expires_at > now:
                valid_bids.append(bid)
            else:
                # Mark as expired
                bid.status = BidStatus.EXPIRED
                bid.updated_at = now
        
        return valid_bids
    
    def get_sales(self) -> List[NFTSale]:
        """Get all sales"""
        return self.sales
    
    def purchase_nft(self, purchase_request: PurchaseRequest) -> AuctionHouseResponse:
        """Purchase an NFT directly"""
        try:
            self.logger.info(f"Purchasing NFT: {purchase_request.nft_mint}")

            # Validate request
            errors = purchase_request.validate()
            if errors:
                raise ValidationError(f"Validation failed: {'; '.join(errors)}")

            # Find active listing
            listings = self.get_listings(purchase_request.nft_mint)
            if not listings:
                raise ValidationError("No active listing found for this NFT")

            # Get the first (cheapest) listing
            listing = min(listings, key=lambda x: x.price)

            # Check if price is acceptable
            if listing.price > purchase_request.max_price:
                raise ValidationError(f"Listing price ({listing.price} SOL) exceeds maximum price ({purchase_request.max_price} SOL)")

            # Load buyer wallet
            buyer_keypair = self.wallet_service.load_keypair()
            buyer_pubkey = buyer_keypair.pubkey()

            # Check buyer has sufficient balance
            balance = self.solana_service.get_balance(buyer_pubkey)
            required_lamports = int(listing.price * 1_000_000_000)

            if balance < required_lamports:
                raise ValidationError(f"Insufficient balance. Required: {listing.price} SOL")

            # Create sale record
            sale_id = str(uuid.uuid4())
            sale = NFTSale(
                sale_id=sale_id,
                nft_mint=purchase_request.nft_mint,
                seller=listing.seller,
                buyer=str(buyer_pubkey),
                price=listing.price,
                currency_mint=purchase_request.currency_mint,
                auction_house=listing.auction_house,
                listing_id=listing.listing_id,
                status=SaleStatus.COMPLETED,  # For demo, mark as completed
                completed_at=datetime.utcnow(),
                transaction_signature="demo_purchase_" + sale_id[:8]  # Demo signature
            )

            # Update listing status
            listing.status = ListingStatus.SOLD
            listing.updated_at = datetime.utcnow()

            # Store sale
            self.sales.append(sale)

            self.logger.info(f"NFT purchased successfully: {sale_id}")

            return AuctionHouseResponse(
                success=True,
                message="NFT purchased successfully!",
                transaction_signature=sale.transaction_signature,
                data=sale.to_dict()
            )

        except Exception as e:
            error_msg = f"Failed to purchase NFT: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return AuctionHouseResponse(
                success=False,
                message=error_msg
            )

    def accept_bid(self, bid_id: str) -> AuctionHouseResponse:
        """Accept a bid on an NFT"""
        try:
            self.logger.info(f"Accepting bid: {bid_id}")

            # Find the bid
            bid = None
            for b in self.bids:
                if b.bid_id == bid_id and b.status == BidStatus.ACTIVE:
                    bid = b
                    break

            if not bid:
                raise ValidationError("Bid not found or not active")

            # Load seller wallet (must be the owner)
            seller_keypair = self.wallet_service.load_keypair()
            seller_pubkey = seller_keypair.pubkey()

            # Check if seller owns the NFT
            nft_mint = Pubkey.from_string(bid.nft_mint)
            ata_address = self.solana_service.get_associated_token_address(seller_pubkey, nft_mint)

            account_info = self.solana_service.client.get_account_info(ata_address)
            if account_info.value is None:
                raise ValidationError("Seller does not own this NFT")

            # Create sale record
            sale_id = str(uuid.uuid4())
            sale = NFTSale(
                sale_id=sale_id,
                nft_mint=bid.nft_mint,
                seller=str(seller_pubkey),
                buyer=bid.bidder,
                price=bid.price,
                currency_mint=bid.currency_mint,
                auction_house=bid.auction_house,
                bid_id=bid.bid_id,
                status=SaleStatus.COMPLETED,  # For demo
                completed_at=datetime.utcnow(),
                transaction_signature="demo_accept_bid_" + sale_id[:8]
            )

            # Update bid status
            bid.status = BidStatus.ACCEPTED
            bid.updated_at = datetime.utcnow()

            # Cancel any active listings for this NFT
            for listing in self.listings:
                if listing.nft_mint == bid.nft_mint and listing.status == ListingStatus.ACTIVE:
                    listing.status = ListingStatus.CANCELLED
                    listing.updated_at = datetime.utcnow()

            # Store sale
            self.sales.append(sale)

            self.logger.info(f"Bid accepted successfully: {sale_id}")

            return AuctionHouseResponse(
                success=True,
                message="Bid accepted successfully!",
                transaction_signature=sale.transaction_signature,
                data=sale.to_dict()
            )

        except Exception as e:
            error_msg = f"Failed to accept bid: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return AuctionHouseResponse(
                success=False,
                message=error_msg
            )

    def cancel_listing(self, listing_id: str) -> AuctionHouseResponse:
        """Cancel an NFT listing"""
        try:
            # Find the listing
            listing = None
            for l in self.listings:
                if l.listing_id == listing_id and l.status == ListingStatus.ACTIVE:
                    listing = l
                    break

            if not listing:
                raise ValidationError("Listing not found or not active")

            # Load seller wallet (must be the owner)
            seller_keypair = self.wallet_service.load_keypair()
            seller_pubkey = seller_keypair.pubkey()

            if listing.seller != str(seller_pubkey):
                raise ValidationError("Only the seller can cancel this listing")

            # Update listing status
            listing.status = ListingStatus.CANCELLED
            listing.updated_at = datetime.utcnow()

            self.logger.info(f"Listing cancelled: {listing_id}")

            return AuctionHouseResponse(
                success=True,
                message="Listing cancelled successfully!",
                data=listing.to_dict()
            )

        except Exception as e:
            error_msg = f"Failed to cancel listing: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return AuctionHouseResponse(
                success=False,
                message=error_msg
            )

    def cancel_bid(self, bid_id: str) -> AuctionHouseResponse:
        """Cancel a bid"""
        try:
            # Find the bid
            bid = None
            for b in self.bids:
                if b.bid_id == bid_id and b.status == BidStatus.ACTIVE:
                    bid = b
                    break

            if not bid:
                raise ValidationError("Bid not found or not active")

            # Load bidder wallet (must be the bidder)
            bidder_keypair = self.wallet_service.load_keypair()
            bidder_pubkey = bidder_keypair.pubkey()

            if bid.bidder != str(bidder_pubkey):
                raise ValidationError("Only the bidder can cancel this bid")

            # Update bid status
            bid.status = BidStatus.CANCELLED
            bid.updated_at = datetime.utcnow()

            self.logger.info(f"Bid cancelled: {bid_id}")

            return AuctionHouseResponse(
                success=True,
                message="Bid cancelled successfully!",
                data=bid.to_dict()
            )

        except Exception as e:
            error_msg = f"Failed to cancel bid: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return AuctionHouseResponse(
                success=False,
                message=error_msg
            )

    def _ensure_data_dir(self):
        """Ensure data directory exists"""
        import os
        os.makedirs(self.data_dir, exist_ok=True)

    def _save_listings(self):
        """Save listings to file"""
        import json
        import os
        filepath = os.path.join(self.data_dir, "listings.json")
        data = [listing.to_dict() for listing in self.listings]
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)

    def _load_listings(self) -> List[NFTListing]:
        """Load listings from file"""
        import json
        import os
        filepath = os.path.join(self.data_dir, "listings.json")
        if not os.path.exists(filepath):
            return []

        try:
            with open(filepath, 'r') as f:
                data = json.load(f)

            listings = []
            for item in data:
                listing = NFTListing(
                    listing_id=item['listing_id'],
                    nft_mint=item['nft_mint'],
                    seller=item['seller'],
                    price=item['price'],
                    currency_mint=item['currency_mint'],
                    status=ListingStatus(item['status']),
                    created_at=datetime.fromisoformat(item['created_at']) if item['created_at'] else None,
                    expires_at=datetime.fromisoformat(item['expires_at']) if item['expires_at'] else None,
                    updated_at=datetime.fromisoformat(item['updated_at']) if item['updated_at'] else None,
                    auction_house=item['auction_house'],
                    seller_trade_state=item['seller_trade_state'],
                    free_seller_trade_state=item['free_seller_trade_state'],
                    metadata=item['metadata']
                )
                listings.append(listing)

            return listings
        except Exception as e:
            self.logger.warning(f"Failed to load listings: {str(e)}")
            return []

    def _save_bids(self):
        """Save bids to file"""
        import json
        import os
        filepath = os.path.join(self.data_dir, "bids.json")
        data = [bid.to_dict() for bid in self.bids]
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)

    def _load_bids(self) -> List[NFTBid]:
        """Load bids from file"""
        import json
        import os
        filepath = os.path.join(self.data_dir, "bids.json")
        if not os.path.exists(filepath):
            return []

        try:
            with open(filepath, 'r') as f:
                data = json.load(f)

            bids = []
            for item in data:
                bid = NFTBid(
                    bid_id=item['bid_id'],
                    nft_mint=item['nft_mint'],
                    bidder=item['bidder'],
                    price=item['price'],
                    currency_mint=item['currency_mint'],
                    status=BidStatus(item['status']),
                    created_at=datetime.fromisoformat(item['created_at']) if item['created_at'] else None,
                    expires_at=datetime.fromisoformat(item['expires_at']) if item['expires_at'] else None,
                    updated_at=datetime.fromisoformat(item['updated_at']) if item['updated_at'] else None,
                    auction_house=item['auction_house'],
                    buyer_trade_state=item['buyer_trade_state'],
                    escrow_payment_account=item['escrow_payment_account']
                )
                bids.append(bid)

            return bids
        except Exception as e:
            self.logger.warning(f"Failed to load bids: {str(e)}")
            return []

    def _save_sales(self):
        """Save sales to file"""
        import json
        import os
        filepath = os.path.join(self.data_dir, "sales.json")
        data = [sale.to_dict() for sale in self.sales]
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)

    def _load_sales(self) -> List[NFTSale]:
        """Load sales from file"""
        import json
        import os
        filepath = os.path.join(self.data_dir, "sales.json")
        if not os.path.exists(filepath):
            return []

        try:
            with open(filepath, 'r') as f:
                data = json.load(f)

            sales = []
            for item in data:
                sale = NFTSale(
                    sale_id=item['sale_id'],
                    nft_mint=item['nft_mint'],
                    seller=item['seller'],
                    buyer=item['buyer'],
                    price=item['price'],
                    currency_mint=item['currency_mint'],
                    status=SaleStatus(item['status']),
                    created_at=datetime.fromisoformat(item['created_at']) if item['created_at'] else None,
                    completed_at=datetime.fromisoformat(item['completed_at']) if item['completed_at'] else None,
                    transaction_signature=item['transaction_signature'],
                    auction_house=item['auction_house'],
                    listing_id=item['listing_id'],
                    bid_id=item['bid_id'],
                    seller_fee=item['seller_fee'],
                    buyer_fee=item['buyer_fee'],
                    royalty_fee=item['royalty_fee']
                )
                sales.append(sale)

            return sales
        except Exception as e:
            self.logger.warning(f"Failed to load sales: {str(e)}")
            return []

    def _save_auction_house_config(self):
        """Save auction house config to file"""
        import json
        import os
        if self.auction_house_config:
            filepath = os.path.join(self.data_dir, "auction_house_config.json")
            with open(filepath, 'w') as f:
                json.dump(self.auction_house_config.to_dict(), f, indent=2)

    def _load_auction_house_config(self) -> Optional[AuctionHouseConfig]:
        """Load auction house config from file"""
        import json
        import os
        filepath = os.path.join(self.data_dir, "auction_house_config.json")
        if not os.path.exists(filepath):
            return None

        try:
            with open(filepath, 'r') as f:
                data = json.load(f)

            return AuctionHouseConfig(
                authority=data['authority'],
                treasury_mint=data['treasury_mint'],
                fee_withdrawal_destination=data['fee_withdrawal_destination'],
                treasury_withdrawal_destination=data['treasury_withdrawal_destination'],
                seller_fee_basis_points=data['seller_fee_basis_points'],
                requires_sign_off=data['requires_sign_off'],
                can_change_sale_price=data['can_change_sale_price']
            )
        except Exception as e:
            self.logger.warning(f"Failed to load auction house config: {str(e)}")
            return None

    def get_auction_house_info(self) -> Optional[AuctionHouseConfig]:
        """Get auction house configuration"""
        return self.auction_house_config
