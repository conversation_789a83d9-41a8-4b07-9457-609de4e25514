# src/services/metaplex_service.py
import struct
import json
from typing import Optional, List
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solders.instruction import Instruction, AccountMeta
from ..models.nft import NFTMetadata
from ..utils.logger import get_logger
from ..utils.exceptions import SolanaAPIError

class MetaplexService:
    """Service for Metaplex Token Metadata Program operations"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Metaplex Token Metadata Program ID
        self.METADATA_PROGRAM_ID = Pubkey.from_string('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s')
        
        # System Program ID
        self.SYSTEM_PROGRAM_ID = Pubkey.from_string('11111111111111111111111111111112')
        
        # Rent Sysvar
        self.SYSVAR_RENT_PUBKEY = Pubkey.from_string('SysvarRent111111111111111111111111111111111')
    
    def get_metadata_pda(self, mint_pubkey: Pubkey) -> Pubkey:
        """Get Metadata Program Derived Address for a mint"""
        seeds = [
            b"metadata",
            bytes(self.METADATA_PROGRAM_ID),
            bytes(mint_pubkey)
        ]
        
        pda, _ = Pubkey.find_program_address(seeds, self.METADATA_PROGRAM_ID)
        return pda
    
    def get_master_edition_pda(self, mint_pubkey: Pubkey) -> Pubkey:
        """Get Master Edition Program Derived Address for a mint"""
        seeds = [
            b"metadata",
            bytes(self.METADATA_PROGRAM_ID),
            bytes(mint_pubkey),
            b"edition"
        ]
        
        pda, _ = Pubkey.find_program_address(seeds, self.METADATA_PROGRAM_ID)
        return pda
    
    def create_metadata_instruction_v3(
        self,
        metadata_pda: Pubkey,
        mint: Pubkey,
        mint_authority: Pubkey,
        payer: Pubkey,
        update_authority: Pubkey,
        metadata: NFTMetadata,
        is_mutable: bool = True
    ) -> Instruction:
        """Create Metaplex CreateMetadataAccountV3 instruction"""
        
        # Serialize metadata
        metadata_data = self._serialize_metadata_v3(metadata, is_mutable)
        
        accounts = [
            AccountMeta(pubkey=metadata_pda, is_signer=False, is_writable=True),
            AccountMeta(pubkey=mint, is_signer=False, is_writable=False),
            AccountMeta(pubkey=mint_authority, is_signer=True, is_writable=False),
            AccountMeta(pubkey=payer, is_signer=True, is_writable=True),
            AccountMeta(pubkey=update_authority, is_signer=False, is_writable=False),
            AccountMeta(pubkey=self.SYSTEM_PROGRAM_ID, is_signer=False, is_writable=False),
            AccountMeta(pubkey=self.SYSVAR_RENT_PUBKEY, is_signer=False, is_writable=False),
        ]
        
        return Instruction(
            program_id=self.METADATA_PROGRAM_ID,
            accounts=accounts,
            data=metadata_data
        )
    
    def create_master_edition_instruction_v3(
        self,
        edition_pda: Pubkey,
        mint: Pubkey,
        update_authority: Pubkey,
        mint_authority: Pubkey,
        payer: Pubkey,
        metadata_pda: Pubkey,
        max_supply: Optional[int] = None
    ) -> Instruction:
        """Create Metaplex CreateMasterEditionV3 instruction"""

        # Instruction discriminator for CreateMasterEditionV3
        data = bytearray()
        data.extend(struct.pack('<B', 17))  # CreateMasterEditionV3 discriminator

        # Max supply (Option<u64>)
        if max_supply is not None:
            data.extend(struct.pack('<B', 1))  # Some
            data.extend(struct.pack('<Q', max_supply))
        else:
            data.extend(struct.pack('<B', 0))  # None
        
        accounts = [
            AccountMeta(pubkey=edition_pda, is_signer=False, is_writable=True),
            AccountMeta(pubkey=mint, is_signer=False, is_writable=True),
            AccountMeta(pubkey=update_authority, is_signer=True, is_writable=False),
            AccountMeta(pubkey=mint_authority, is_signer=True, is_writable=False),
            AccountMeta(pubkey=payer, is_signer=True, is_writable=True),
            AccountMeta(pubkey=metadata_pda, is_signer=False, is_writable=False),
            AccountMeta(pubkey=Pubkey.from_string('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'), is_signer=False, is_writable=False),
            AccountMeta(pubkey=self.SYSTEM_PROGRAM_ID, is_signer=False, is_writable=False),
            AccountMeta(pubkey=self.SYSVAR_RENT_PUBKEY, is_signer=False, is_writable=False),
        ]
        
        return Instruction(
            program_id=self.METADATA_PROGRAM_ID,
            accounts=accounts,
            data=bytes(data)
        )
    
    def _serialize_metadata_v3(self, metadata: NFTMetadata, is_mutable: bool) -> bytes:
        """Serialize metadata for CreateMetadataAccountV3 instruction"""

        # Use proper Metaplex instruction format
        # CreateMetadataAccountV3 instruction layout

        # Start with instruction discriminator
        data = bytearray()

        # Instruction index for CreateMetadataAccountV3 (33 in decimal, 0x21 in hex)
        data.extend(struct.pack('<B', 33))

        # DataV2 struct serialization
        # Name (string)
        name_bytes = metadata.name.encode('utf-8')[:32]  # Max 32 bytes
        data.extend(struct.pack('<I', len(name_bytes)))
        data.extend(name_bytes)

        # Symbol (string)
        symbol_bytes = metadata.symbol.encode('utf-8')[:10]  # Max 10 bytes
        data.extend(struct.pack('<I', len(symbol_bytes)))
        data.extend(symbol_bytes)

        # URI (string) - metadata JSON URI
        uri_bytes = metadata.image.encode('utf-8')[:200]  # Max 200 bytes
        data.extend(struct.pack('<I', len(uri_bytes)))
        data.extend(uri_bytes)

        # Seller fee basis points (u16)
        data.extend(struct.pack('<H', metadata.seller_fee_basis_points))

        # Creators (Option<Vec<Creator>>)
        if metadata.properties and metadata.properties.creators:
            data.extend(struct.pack('<B', 1))  # Some
            data.extend(struct.pack('<I', len(metadata.properties.creators)))

            for creator in metadata.properties.creators:
                # Creator address (32 bytes)
                try:
                    creator_pubkey = Pubkey.from_string(creator.address)
                    data.extend(bytes(creator_pubkey))
                except:
                    # If invalid address, use system program
                    data.extend(bytes(self.SYSTEM_PROGRAM_ID))

                # Verified (bool)
                data.extend(struct.pack('<B', 1 if creator.verified else 0))

                # Share (u8)
                data.extend(struct.pack('<B', min(creator.share, 100)))
        else:
            data.extend(struct.pack('<B', 0))  # None

        # Collection (Option<Collection>)
        data.extend(struct.pack('<B', 0))  # None for now

        # Uses (Option<Uses>)
        data.extend(struct.pack('<B', 0))  # None

        # Collection details (Option<CollectionDetails>)
        data.extend(struct.pack('<B', 0))  # None

        # Is mutable (bool)
        data.extend(struct.pack('<B', 1 if is_mutable else 0))

        return bytes(data)
    
    def _serialize_string(self, s: str) -> bytes:
        """Serialize a string for Solana instruction data"""
        encoded = s.encode('utf-8')
        return struct.pack('<I', len(encoded)) + encoded
    
    def upload_metadata_to_storage(self, metadata: NFTMetadata) -> str:
        """Upload metadata to storage and return URI"""
        import os
        import hashlib

        # Create metadata JSON
        metadata_dict = metadata.to_dict()
        metadata_json = json.dumps(metadata_dict, indent=2)

        # Create a hash for the filename
        content_hash = hashlib.md5(metadata_json.encode()).hexdigest()[:8]

        # Create metadata directory if it doesn't exist
        metadata_dir = "metadata"
        os.makedirs(metadata_dir, exist_ok=True)

        # Save metadata to local file
        filename = f"nft_metadata_{content_hash}.json"
        filepath = os.path.join(metadata_dir, filename)

        with open(filepath, 'w') as f:
            f.write(metadata_json)

        # For development, return a local file URI
        # In production, you would upload to IPFS, Arweave, or cloud storage
        local_uri = f"file://{os.path.abspath(filepath)}"

        self.logger.info(f"Metadata saved to: {filepath}")
        self.logger.info(f"Metadata content: {metadata_json}")
        self.logger.warning("Using local file storage. In production, upload to IPFS/Arweave")

        # Return a development URI that can be used for testing
        return f"https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png"
    
    def create_nft_instructions(
        self,
        mint: Pubkey,
        mint_authority: Pubkey,
        payer: Pubkey,
        update_authority: Pubkey,
        metadata: NFTMetadata,
        is_mutable: bool = True,
        max_supply: Optional[int] = None
    ):
        """Create all instructions needed for NFT creation"""
        
        instructions = []
        
        # Get PDAs
        metadata_pda = self.get_metadata_pda(mint)
        master_edition_pda = self.get_master_edition_pda(mint)
        
        # Create metadata account
        metadata_ix = self.create_metadata_instruction_v3(
            metadata_pda=metadata_pda,
            mint=mint,
            mint_authority=mint_authority,
            payer=payer,
            update_authority=update_authority,
            metadata=metadata,
            is_mutable=is_mutable
        )
        instructions.append(metadata_ix)
        
        # Create master edition (for NFTs)
        master_edition_ix = self.create_master_edition_instruction_v3(
            edition_pda=master_edition_pda,
            mint=mint,
            update_authority=update_authority,
            mint_authority=mint_authority,
            payer=payer,
            metadata_pda=metadata_pda,
            max_supply=max_supply
        )
        instructions.append(master_edition_ix)
        
        return instructions, metadata_pda, master_edition_pda
