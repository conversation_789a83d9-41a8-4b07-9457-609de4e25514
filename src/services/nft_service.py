# src/services/nft_service.py
from datetime import datetime
from typing import Optional
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from ..models.nft import NFTRequest, NFTResponse, NFTMetadata
from ..models.transaction import TransactionResult
from .solana_service import SolanaService
from .wallet_service import WalletService
from .metaplex_service import MetaplexService
from ..utils.logger import get_logger
from ..utils.helpers import generate_explorer_url
from ..utils.exceptions import TransactionError, ValidationError

class NFTService:
    """Service for NFT operations"""
    
    def __init__(self, solana_service: SolanaService, wallet_service: WalletService):
        self.solana_service = solana_service
        self.wallet_service = wallet_service
        self.metaplex_service = MetaplexService()
        self.logger = get_logger(__name__)
    
    def create_nft(self, nft_request: NFTRequest) -> NFTResponse:
        """Create a new NFT with Metaplex metadata"""
        try:
            self.logger.info(f"Creating NFT: {nft_request.name} ({nft_request.symbol})")
            
            # Load payer wallet
            payer_keypair = self.wallet_service.load_keypair()
            payer_pubkey = payer_keypair.pubkey()
            
            # Check balance
            estimated_cost = 0.01  # Estimated SOL cost for NFT creation (higher than token)
            self.solana_service.check_sufficient_balance(
                payer_pubkey, 
                int(estimated_cost * 1_000_000_000)
            )
            
            # Generate new mint keypair
            mint_keypair = Keypair()
            mint_pubkey = mint_keypair.pubkey()
            
            self.logger.info(f"New NFT mint address: {mint_pubkey}")
            
            # Convert request to metadata
            metadata = nft_request.to_nft_metadata(str(payer_pubkey))
            
            # Upload metadata to storage (placeholder for now)
            metadata_uri = self.metaplex_service.upload_metadata_to_storage(metadata)
            
            # Create instructions
            instructions = []
            
            # 1. Create and initialize mint account (0 decimals for NFT)
            mint_instructions = self.solana_service.create_mint_instructions(
                payer_pubkey,
                mint_pubkey,
                payer_pubkey,  # mint authority
                payer_pubkey   # freeze authority
            )
            instructions.extend(mint_instructions)
            
            # 2. Create associated token account
            ata_address = self.solana_service.get_associated_token_address(payer_pubkey, mint_pubkey)
            create_ata_ix = self.solana_service.create_associated_token_account_instruction(
                payer_pubkey,
                payer_pubkey,
                mint_pubkey
            )
            instructions.append(create_ata_ix)
            
            # 3. Mint 1 token to ATA (NFTs have supply of 1)
            mint_to_ix = self.solana_service.create_mint_to_instruction(
                mint_pubkey,
                ata_address,
                payer_pubkey,
                1  # NFTs have supply of 1
            )
            instructions.append(mint_to_ix)
            
            # 4. Create Metaplex metadata and master edition
            metaplex_instructions, metadata_pda, master_edition_pda = self.metaplex_service.create_nft_instructions(
                mint=mint_pubkey,
                mint_authority=payer_pubkey,
                payer=payer_pubkey,
                update_authority=payer_pubkey,
                metadata=metadata,
                is_mutable=nft_request.is_mutable,
                max_supply=nft_request.max_supply
            )
            instructions.extend(metaplex_instructions)
            
            # Build and send transaction
            transaction = self.solana_service.build_transaction(
                instructions,
                payer_pubkey,
                [payer_keypair, mint_keypair]
            )
            
            # Send transaction
            signature = self.solana_service.send_transaction(transaction)
            signature_str = str(signature)
            
            # Confirm transaction
            tx_result = self.solana_service.confirm_transaction(signature)
            
            if not tx_result.is_successful:
                error_msg = f"Transaction failed: {tx_result.err}"
                self.logger.error(error_msg)
                return NFTResponse(
                    success=False,
                    message=error_msg,
                    transaction_signature=signature_str
                )
            
            # Create successful response
            explorer_url = generate_explorer_url(signature_str, self.solana_service.network)
            
            response = NFTResponse(
                success=True,
                message="NFT created successfully!",
                nft_mint_address=str(mint_pubkey),
                metadata_pda=str(metadata_pda),
                metadata_uri=metadata_uri,
                associated_token_account=str(ata_address),
                transaction_signature=signature_str,
                explorer_url=explorer_url,
                nft_details={
                    "name": nft_request.name,
                    "symbol": nft_request.symbol,
                    "description": nft_request.description,
                    "image": nft_request.image_url,
                    "is_mutable": nft_request.is_mutable,
                    "max_supply": nft_request.max_supply,
                    "mint_authority": str(payer_pubkey),
                    "update_authority": str(payer_pubkey),
                    "master_edition": str(master_edition_pda)
                },
                created_at=datetime.utcnow()
            )
            
            self.logger.info(f"NFT created successfully: {mint_pubkey}")
            return response
            
        except Exception as e:
            error_msg = f"Failed to create NFT: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return NFTResponse(
                success=False,
                message=error_msg
            )
    
    def get_nft_info(self, mint_address: str) -> dict:
        """Get NFT information including metadata"""
        try:
            mint_pubkey = Pubkey.from_string(mint_address)
            
            # Get mint account info
            mint_response = self.solana_service.client.get_account_info(mint_pubkey)
            
            if mint_response.value is None:
                return {"error": "NFT mint not found"}
            
            # Get metadata PDA
            metadata_pda = self.metaplex_service.get_metadata_pda(mint_pubkey)
            
            # Get metadata account info
            metadata_response = self.solana_service.client.get_account_info(metadata_pda)
            
            # Get master edition PDA
            master_edition_pda = self.metaplex_service.get_master_edition_pda(mint_pubkey)
            
            # Get master edition account info
            master_edition_response = self.solana_service.client.get_account_info(master_edition_pda)
            
            return {
                "mint_address": mint_address,
                "metadata_pda": str(metadata_pda),
                "master_edition_pda": str(master_edition_pda),
                "mint_account_exists": mint_response.value is not None,
                "metadata_account_exists": metadata_response.value is not None,
                "master_edition_exists": master_edition_response.value is not None,
                "mint_lamports": mint_response.value.lamports if mint_response.value else 0,
                "metadata_lamports": metadata_response.value.lamports if metadata_response.value else 0
            }
            
        except Exception as e:
            return {"error": f"Failed to get NFT info: {str(e)}"}
    
    def get_nft_balance(self, owner_address: str, mint_address: str) -> dict:
        """Get NFT balance for an owner (should be 0 or 1 for NFTs)"""
        try:
            owner_pubkey = Pubkey.from_string(owner_address)
            mint_pubkey = Pubkey.from_string(mint_address)
            
            # Get associated token account
            ata_address = self.solana_service.get_associated_token_address(owner_pubkey, mint_pubkey)
            
            # Get account info
            response = self.solana_service.client.get_account_info(ata_address)
            
            if response.value is None:
                return {
                    "owner": owner_address,
                    "mint": mint_address,
                    "balance": 0,
                    "associated_token_account": str(ata_address),
                    "exists": False
                }
            
            # For NFTs, balance should be 0 or 1
            # In a full implementation, you'd parse the token account data
            return {
                "owner": owner_address,
                "mint": mint_address,
                "balance": 1,  # Assume 1 if account exists
                "associated_token_account": str(ata_address),
                "exists": True,
                "lamports": response.value.lamports
            }
            
        except Exception as e:
            return {"error": f"Failed to get NFT balance: {str(e)}"}
    
    def validate_nft_request(self, request_data: dict) -> NFTRequest:
        """Validate and create NFTRequest from request data"""
        
        # Extract required fields
        name = request_data.get('name', '').strip()
        symbol = request_data.get('symbol', '').strip()
        description = request_data.get('description', '').strip()
        image_url = request_data.get('image_url', '').strip()
        
        # Create NFTRequest object
        nft_request = NFTRequest(
            name=name,
            symbol=symbol,
            description=description,
            image_url=image_url,
            external_url=request_data.get('external_url'),
            animation_url=request_data.get('animation_url'),
            attributes=request_data.get('attributes', []),
            collection_name=request_data.get('collection_name'),
            collection_family=request_data.get('collection_family'),
            seller_fee_basis_points=request_data.get('seller_fee_basis_points', 0),
            is_mutable=request_data.get('is_mutable', True),
            max_supply=request_data.get('max_supply')
        )
        
        # Validate the request
        errors = nft_request.validate()
        if errors:
            raise ValidationError(f"Validation failed: {'; '.join(errors)}")
        
        return nft_request
