# src/services/simple_nft_service.py
from datetime import datetime
from typing import Optional
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from ..models.nft import NFTRequest, NFTResponse
from ..models.token import TokenRequest
from .solana_service import SolanaService
from .wallet_service import WalletService
from .token_service import TokenService
from ..utils.logger import get_logger
from ..utils.helpers import generate_explorer_url
from ..utils.exceptions import TransactionError, ValidationError

class SimpleNFTService:
    """Service for creating simple NFT-like tokens (SPL tokens with 0 decimals and supply of 1)"""
    
    def __init__(self, solana_service: SolanaService, wallet_service: WalletService):
        self.solana_service = solana_service
        self.wallet_service = wallet_service
        self.token_service = TokenService(solana_service, wallet_service)
        self.logger = get_logger(__name__)
    
    def create_simple_nft(self, nft_request: NFTRequest) -> NFTResponse:
        """Create a simple NFT-like token"""
        try:
            self.logger.info(f"Creating simple NFT: {nft_request.name} ({nft_request.symbol})")
            
            # Create token request for NFT-like token
            token_request = TokenRequest(
                name=nft_request.name,
                symbol=nft_request.symbol,
                amount=1,  # NFTs have supply of 1
                description=nft_request.description
            )
            
            # Create the token
            token_response = self.token_service.create_token(token_request)
            
            if not token_response.success:
                return NFTResponse(
                    success=False,
                    message=f"Failed to create NFT token: {token_response.message}",
                    transaction_signature=token_response.transaction_signature
                )
            
            # Save metadata to local storage
            metadata_uri = self._save_nft_metadata(nft_request)
            
            # Create NFT response
            response = NFTResponse(
                success=True,
                message="Simple NFT created successfully!",
                nft_mint_address=token_response.token_mint_address,
                metadata_uri=metadata_uri,
                associated_token_account=token_response.associated_token_account,
                transaction_signature=token_response.transaction_signature,
                explorer_url=token_response.explorer_url,
                nft_details={
                    "name": nft_request.name,
                    "symbol": nft_request.symbol,
                    "description": nft_request.description,
                    "image": nft_request.image_url,
                    "attributes": nft_request.attributes,
                    "external_url": nft_request.external_url,
                    "animation_url": nft_request.animation_url,
                    "collection_name": nft_request.collection_name,
                    "seller_fee_basis_points": nft_request.seller_fee_basis_points,
                    "is_mutable": nft_request.is_mutable,
                    "max_supply": nft_request.max_supply,
                    "type": "simple_nft",
                    "note": "This is a simple NFT-like token (SPL token with 0 decimals and supply of 1)"
                },
                created_at=datetime.utcnow()
            )
            
            self.logger.info(f"Simple NFT created successfully: {token_response.token_mint_address}")
            return response
            
        except Exception as e:
            error_msg = f"Failed to create simple NFT: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return NFTResponse(
                success=False,
                message=error_msg
            )
    
    def _save_nft_metadata(self, nft_request: NFTRequest) -> str:
        """Save NFT metadata to local storage and return URI"""
        import os
        import json
        import hashlib
        
        # Create metadata dictionary
        metadata = {
            "name": nft_request.name,
            "symbol": nft_request.symbol,
            "description": nft_request.description,
            "image": nft_request.image_url,
            "external_url": nft_request.external_url,
            "animation_url": nft_request.animation_url,
            "attributes": nft_request.attributes,
            "properties": {
                "files": [
                    {
                        "uri": nft_request.image_url,
                        "type": "image/png"
                    }
                ],
                "category": "image"
            },
            "seller_fee_basis_points": nft_request.seller_fee_basis_points
        }
        
        if nft_request.collection_name:
            metadata["collection"] = {
                "name": nft_request.collection_name,
                "family": nft_request.collection_family
            }
        
        # Create metadata JSON
        metadata_json = json.dumps(metadata, indent=2)
        
        # Create hash for filename
        content_hash = hashlib.md5(metadata_json.encode()).hexdigest()[:8]
        
        # Create metadata directory
        metadata_dir = "metadata"
        os.makedirs(metadata_dir, exist_ok=True)
        
        # Save to file
        filename = f"simple_nft_{content_hash}.json"
        filepath = os.path.join(metadata_dir, filename)
        
        with open(filepath, 'w') as f:
            f.write(metadata_json)
        
        self.logger.info(f"NFT metadata saved to: {filepath}")
        
        # Return local file path as URI
        return f"file://{os.path.abspath(filepath)}"
    
    def get_nft_info(self, mint_address: str) -> dict:
        """Get simple NFT information"""
        try:
            # Use token service to get basic token info
            token_info = self.token_service.get_token_info(mint_address)
            
            if "error" in token_info:
                return token_info
            
            # Add NFT-specific information
            token_info["type"] = "simple_nft"
            token_info["note"] = "This is a simple NFT-like token"
            
            # Try to find metadata file
            metadata_file = self._find_metadata_file(mint_address)
            if metadata_file:
                token_info["metadata_file"] = metadata_file
                
                # Load metadata if file exists
                try:
                    import json
                    with open(metadata_file, 'r') as f:
                        metadata = json.load(f)
                    token_info["metadata"] = metadata
                except Exception as e:
                    token_info["metadata_error"] = str(e)
            
            return token_info
            
        except Exception as e:
            return {"error": f"Failed to get NFT info: {str(e)}"}
    
    def _find_metadata_file(self, mint_address: str) -> Optional[str]:
        """Find metadata file for a mint address"""
        import os
        import glob
        
        metadata_dir = "metadata"
        if not os.path.exists(metadata_dir):
            return None
        
        # Look for metadata files
        pattern = os.path.join(metadata_dir, "simple_nft_*.json")
        files = glob.glob(pattern)
        
        # For now, just return the first file found
        # In a real implementation, you'd store the mapping between mint and metadata
        if files:
            return files[0]
        
        return None
    
    def validate_nft_request(self, request_data: dict) -> NFTRequest:
        """Validate and create NFTRequest from request data"""
        
        # Extract required fields
        name = request_data.get('name', '').strip()
        symbol = request_data.get('symbol', '').strip()
        description = request_data.get('description', '').strip()
        image_url = request_data.get('image_url', '').strip()
        
        # Create NFTRequest object
        nft_request = NFTRequest(
            name=name,
            symbol=symbol,
            description=description,
            image_url=image_url,
            external_url=request_data.get('external_url'),
            animation_url=request_data.get('animation_url'),
            attributes=request_data.get('attributes', []),
            collection_name=request_data.get('collection_name'),
            collection_family=request_data.get('collection_family'),
            seller_fee_basis_points=request_data.get('seller_fee_basis_points', 0),
            is_mutable=request_data.get('is_mutable', True),
            max_supply=request_data.get('max_supply')
        )
        
        # Validate the request
        errors = nft_request.validate()
        if errors:
            raise ValidationError(f"Validation failed: {'; '.join(errors)}")
        
        return nft_request
    
    def list_created_nfts(self) -> dict:
        """List all created NFTs with their metadata"""
        import os
        import json
        import glob
        
        metadata_dir = "metadata"
        if not os.path.exists(metadata_dir):
            return {"nfts": [], "count": 0}
        
        nfts = []
        pattern = os.path.join(metadata_dir, "simple_nft_*.json")
        files = glob.glob(pattern)
        
        for file_path in files:
            try:
                with open(file_path, 'r') as f:
                    metadata = json.load(f)
                
                nfts.append({
                    "metadata_file": file_path,
                    "metadata": metadata,
                    "created_at": os.path.getctime(file_path)
                })
            except Exception as e:
                self.logger.warning(f"Failed to load metadata from {file_path}: {str(e)}")
        
        return {
            "nfts": nfts,
            "count": len(nfts),
            "metadata_directory": metadata_dir
        }
