# src/services/solana_service.py
import struct
from typing import List, Optional, Tuple
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solders.system_program import ID as SYSTEM_PROGRAM_ID
from solders.instruction import Instruction, AccountMeta
from solders.message import Message
from solders.transaction import VersionedTransaction
from solana.rpc.api import Client
from solana.rpc.commitment import Confirmed
from ..models.transaction import TransactionResult, TransactionStatus
from ..utils.exceptions import NetworkError, TransactionError, InsufficientFundsError
from ..utils.logger import get_logger
from ..utils.helpers import generate_explorer_url

class SolanaService:
    """Service for Solana blockchain operations"""
    
    def __init__(self, rpc_url: str, network: str = 'devnet'):
        self.rpc_url = rpc_url
        self.network = network
        self.client = Client(rpc_url, commitment=Confirmed)
        self.logger = get_logger(__name__)
        
        # Program IDs
        self.TOKEN_PROGRAM_ID = Pubkey.from_string('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA')
        self.ASSOCIATED_TOKEN_PROGRAM_ID = Pubkey.from_string('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL')
        self.SYSVAR_RENT_PUBKEY = Pubkey.from_string('SysvarRent111111111111111111111111111111111')
    
    def get_balance(self, pubkey: Pubkey) -> float:
        """Get SOL balance for a public key"""
        try:
            response = self.client.get_balance(pubkey)
            lamports = response.value
            return lamports / 1_000_000_000  # Convert to SOL
        except Exception as e:
            raise NetworkError(f"Failed to get balance: {str(e)}", self.rpc_url)
    
    def get_latest_blockhash(self):
        """Get latest blockhash"""
        try:
            response = self.client.get_latest_blockhash()
            return response.value.blockhash
        except Exception as e:
            raise NetworkError(f"Failed to get latest blockhash: {str(e)}", self.rpc_url)
    
    def get_minimum_balance_for_rent_exemption(self, space: int) -> int:
        """Get minimum balance for rent exemption"""
        try:
            response = self.client.get_minimum_balance_for_rent_exemption(space)
            return response.value
        except Exception as e:
            raise NetworkError(f"Failed to get rent exemption: {str(e)}", self.rpc_url)
    
    def send_transaction(self, transaction: VersionedTransaction) -> str:
        """Send transaction to Solana network"""
        try:
            response = self.client.send_transaction(transaction)
            signature = response.value
            self.logger.info(f"Transaction sent: {signature}")
            return signature
        except Exception as e:
            raise TransactionError(f"Failed to send transaction: {str(e)}")
    
    def confirm_transaction(self, signature) -> TransactionResult:
        """Confirm transaction and return result"""
        try:
            # Convert signature to string if needed
            signature_str = str(signature)

            # Wait for confirmation
            self.client.confirm_transaction(signature, commitment=Confirmed)

            # Get transaction details
            response = self.client.get_transaction(signature, commitment=Confirmed)

            if response.value is None:
                return TransactionResult(
                    signature=signature,
                    status=TransactionStatus.FAILED,
                    err={"message": "Transaction not found"}
                )

            tx_data = response.value

            # Determine status - handle different response structures
            try:
                if hasattr(tx_data, 'meta') and tx_data.meta and hasattr(tx_data.meta, 'err') and tx_data.meta.err:
                    status = TransactionStatus.FAILED
                    err = tx_data.meta.err
                    logs = tx_data.meta.log_messages if hasattr(tx_data.meta, 'log_messages') else None
                elif hasattr(tx_data, 'transaction') and hasattr(tx_data.transaction, 'meta'):
                    # Alternative structure
                    meta = tx_data.transaction.meta
                    if meta and hasattr(meta, 'err') and meta.err:
                        status = TransactionStatus.FAILED
                        err = meta.err
                    else:
                        status = TransactionStatus.CONFIRMED
                        err = None
                    logs = meta.log_messages if meta and hasattr(meta, 'log_messages') else None
                else:
                    # Assume success if no error found
                    status = TransactionStatus.CONFIRMED
                    err = None
                    logs = None
            except Exception as parse_error:
                self.logger.warning(f"Error parsing transaction data: {str(parse_error)}")
                status = TransactionStatus.CONFIRMED  # Assume success if we can't parse
                err = None
                logs = None

            return TransactionResult(
                signature=signature_str,
                status=status,
                block_height=getattr(tx_data, 'block_height', None),
                slot=getattr(tx_data, 'slot', None),
                err=err,
                logs=logs
            )

        except Exception as e:
            self.logger.error(f"Error confirming transaction {signature}: {str(e)}")
            return TransactionResult(
                signature=str(signature),
                status=TransactionStatus.FAILED,
                err={"message": str(e)}
            )
    
    def get_associated_token_address(self, owner: Pubkey, mint: Pubkey) -> Pubkey:
        """Get associated token account address"""
        return Pubkey.find_program_address(
            [bytes(owner), bytes(self.TOKEN_PROGRAM_ID), bytes(mint)],
            self.ASSOCIATED_TOKEN_PROGRAM_ID
        )[0]
    
    def create_account_instruction(self, payer: Pubkey, new_account: Pubkey, space: int, owner: Pubkey) -> Instruction:
        """Create system program create account instruction"""
        lamports = self.get_minimum_balance_for_rent_exemption(space)
        
        data = struct.pack('<I', 0)  # CreateAccount instruction
        data += struct.pack('<Q', lamports)  # lamports
        data += struct.pack('<Q', space)  # space
        data += bytes(owner)  # owner program
        
        return Instruction(
            program_id=SYSTEM_PROGRAM_ID,
            accounts=[
                AccountMeta(pubkey=payer, is_signer=True, is_writable=True),
                AccountMeta(pubkey=new_account, is_signer=True, is_writable=True),
            ],
            data=data
        )
    
    def create_mint_instructions(self, payer: Pubkey, mint: Pubkey, mint_authority: Pubkey, freeze_authority: Optional[Pubkey] = None) -> List[Instruction]:
        """Create instructions to create and initialize a mint account"""
        space = 82  # Mint account size
        
        # Create account instruction
        create_account_ix = self.create_account_instruction(payer, mint, space, self.TOKEN_PROGRAM_ID)
        
        # Initialize mint instruction
        init_mint_data = struct.pack('<B', 0)  # InitializeMint instruction
        init_mint_data += struct.pack('<B', 0)  # decimals (0 for NFT)
        init_mint_data += bytes(mint_authority)  # mint authority
        
        if freeze_authority:
            init_mint_data += b'\x01'  # freeze authority option (Some)
            init_mint_data += bytes(freeze_authority)
        else:
            init_mint_data += b'\x00'  # freeze authority option (None)
        
        init_mint_ix = Instruction(
            program_id=self.TOKEN_PROGRAM_ID,
            accounts=[
                AccountMeta(pubkey=mint, is_signer=False, is_writable=True),
                AccountMeta(pubkey=self.SYSVAR_RENT_PUBKEY, is_signer=False, is_writable=False),
            ],
            data=init_mint_data
        )
        
        return [create_account_ix, init_mint_ix]
    
    def create_associated_token_account_instruction(self, payer: Pubkey, owner: Pubkey, mint: Pubkey) -> Instruction:
        """Create instruction to create associated token account"""
        ata_address = self.get_associated_token_address(owner, mint)
        
        return Instruction(
            program_id=self.ASSOCIATED_TOKEN_PROGRAM_ID,
            accounts=[
                AccountMeta(pubkey=payer, is_signer=True, is_writable=True),
                AccountMeta(pubkey=ata_address, is_signer=False, is_writable=True),
                AccountMeta(pubkey=owner, is_signer=False, is_writable=False),
                AccountMeta(pubkey=mint, is_signer=False, is_writable=False),
                AccountMeta(pubkey=SYSTEM_PROGRAM_ID, is_signer=False, is_writable=False),
                AccountMeta(pubkey=self.TOKEN_PROGRAM_ID, is_signer=False, is_writable=False),
                AccountMeta(pubkey=self.SYSVAR_RENT_PUBKEY, is_signer=False, is_writable=False),
            ],
            data=b''  # No data needed for ATA creation
        )
    
    def create_mint_to_instruction(self, mint: Pubkey, dest: Pubkey, authority: Pubkey, amount: int) -> Instruction:
        """Create instruction to mint tokens"""
        mint_to_data = struct.pack('<B', 7)  # MintTo instruction
        mint_to_data += struct.pack('<Q', amount)  # amount
        
        return Instruction(
            program_id=self.TOKEN_PROGRAM_ID,
            accounts=[
                AccountMeta(pubkey=mint, is_signer=False, is_writable=True),
                AccountMeta(pubkey=dest, is_signer=False, is_writable=True),
                AccountMeta(pubkey=authority, is_signer=True, is_writable=False),
            ],
            data=mint_to_data
        )
    
    def build_transaction(self, instructions: List[Instruction], payer: Pubkey, signers: List[Keypair]) -> VersionedTransaction:
        """Build a versioned transaction"""
        try:
            blockhash = self.get_latest_blockhash()
            
            message = Message.new_with_blockhash(
                instructions,
                payer,
                blockhash
            )
            
            return VersionedTransaction(message, signers)
            
        except Exception as e:
            raise TransactionError(f"Failed to build transaction: {str(e)}")
    
    def check_sufficient_balance(self, payer: Pubkey, required_lamports: int) -> None:
        """Check if payer has sufficient balance"""
        balance = self.get_balance(payer)
        balance_lamports = int(balance * 1_000_000_000)
        
        if balance_lamports < required_lamports:
            raise InsufficientFundsError(
                f"Insufficient funds. Required: {required_lamports / 1_000_000_000:.9f} SOL, Available: {balance:.9f} SOL",
                required_lamports / 1_000_000_000,
                balance
            )
