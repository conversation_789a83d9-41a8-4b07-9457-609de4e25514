# src/services/token_service.py
from datetime import datetime
from typing import Optional
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from ..models.token import TokenRequest, TokenResponse, TokenMetadata
from ..models.transaction import TransactionResult
from .solana_service import SolanaService
from .wallet_service import WalletService
from ..utils.logger import get_logger
from ..utils.helpers import generate_explorer_url
from ..utils.exceptions import TransactionError, ValidationError

class TokenService:
    """Service for token operations"""
    
    def __init__(self, solana_service: SolanaService, wallet_service: WalletService):
        self.solana_service = solana_service
        self.wallet_service = wallet_service
        self.logger = get_logger(__name__)
    
    def create_token(self, token_request: TokenRequest) -> TokenResponse:
        """Create a new SPL token"""
        try:
            self.logger.info(f"Creating token: {token_request.name} ({token_request.symbol})")
            
            # Load payer wallet
            payer_keypair = self.wallet_service.load_keypair()
            payer_pubkey = payer_keypair.pubkey()
            
            # Check balance
            estimated_cost = 0.002  # Estimated SOL cost for token creation
            self.solana_service.check_sufficient_balance(
                payer_pubkey, 
                int(estimated_cost * 1_000_000_000)
            )
            
            # Generate new mint keypair
            mint_keypair = Keypair()
            mint_pubkey = mint_keypair.pubkey()
            
            self.logger.info(f"New mint address: {mint_pubkey}")
            
            # Create instructions
            instructions = []
            
            # 1. Create and initialize mint account
            mint_instructions = self.solana_service.create_mint_instructions(
                payer_pubkey,
                mint_pubkey,
                payer_pubkey,  # mint authority
                payer_pubkey   # freeze authority
            )
            instructions.extend(mint_instructions)
            
            # 2. Create associated token account
            ata_address = self.solana_service.get_associated_token_address(payer_pubkey, mint_pubkey)
            create_ata_ix = self.solana_service.create_associated_token_account_instruction(
                payer_pubkey,
                payer_pubkey,
                mint_pubkey
            )
            instructions.append(create_ata_ix)
            
            # 3. Mint tokens to ATA
            mint_to_ix = self.solana_service.create_mint_to_instruction(
                mint_pubkey,
                ata_address,
                payer_pubkey,
                token_request.amount
            )
            instructions.append(mint_to_ix)
            
            # Build and send transaction
            transaction = self.solana_service.build_transaction(
                instructions,
                payer_pubkey,
                [payer_keypair, mint_keypair]
            )
            
            # Send transaction
            signature = self.solana_service.send_transaction(transaction)
            signature_str = str(signature)  # Convert to string immediately

            # Confirm transaction
            tx_result = self.solana_service.confirm_transaction(signature)
            
            if not tx_result.is_successful:
                error_msg = f"Transaction failed: {tx_result.err}"
                self.logger.error(error_msg)
                return TokenResponse(
                    success=False,
                    message=error_msg,
                    transaction_signature=signature_str
                )
            
            # Create successful response
            explorer_url = generate_explorer_url(signature_str, self.solana_service.network)
            
            response = TokenResponse(
                success=True,
                message="Token created successfully!",
                token_mint_address=str(mint_pubkey),
                associated_token_account=str(ata_address),
                transaction_signature=signature_str,
                explorer_url=explorer_url,
                token_details={
                    "name": token_request.name,
                    "symbol": token_request.symbol,
                    "amount_minted": token_request.amount,
                    "decimals": 0,
                    "mint_authority": str(payer_pubkey),
                    "freeze_authority": str(payer_pubkey)
                },
                created_at=datetime.utcnow()
            )
            
            self.logger.info(f"Token created successfully: {mint_pubkey}")
            return response
            
        except Exception as e:
            error_msg = f"Failed to create token: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return TokenResponse(
                success=False,
                message=error_msg
            )
    
    def get_token_info(self, mint_address: str) -> dict:
        """Get token information"""
        try:
            mint_pubkey = Pubkey.from_string(mint_address)
            
            # Get mint account info
            response = self.solana_service.client.get_account_info(mint_pubkey)
            
            if response.value is None:
                return {"error": "Token mint not found"}
            
            # Parse mint account data (simplified)
            account_data = response.value.data
            
            return {
                "mint_address": mint_address,
                "owner": str(response.value.owner),
                "lamports": response.value.lamports,
                "data_length": len(account_data),
                "executable": response.value.executable
            }
            
        except Exception as e:
            return {"error": f"Failed to get token info: {str(e)}"}
    
    def get_token_balance(self, owner_address: str, mint_address: str) -> dict:
        """Get token balance for an owner"""
        try:
            owner_pubkey = Pubkey.from_string(owner_address)
            mint_pubkey = Pubkey.from_string(mint_address)
            
            # Get associated token account
            ata_address = self.solana_service.get_associated_token_address(owner_pubkey, mint_pubkey)
            
            # Get account info
            response = self.solana_service.client.get_account_info(ata_address)
            
            if response.value is None:
                return {
                    "owner": owner_address,
                    "mint": mint_address,
                    "balance": 0,
                    "associated_token_account": str(ata_address),
                    "exists": False
                }
            
            # Parse token account data (simplified - would need proper parsing in production)
            return {
                "owner": owner_address,
                "mint": mint_address,
                "balance": "Unknown",  # Would need proper parsing
                "associated_token_account": str(ata_address),
                "exists": True
            }
            
        except Exception as e:
            return {"error": f"Failed to get token balance: {str(e)}"}
    
    def create_metadata(self, token_request: TokenRequest) -> TokenMetadata:
        """Create token metadata object"""
        return TokenMetadata(
            name=token_request.name,
            symbol=token_request.symbol,
            description=token_request.description,
            image=token_request.image_uri,
            attributes=token_request.attributes,
            seller_fee_basis_points=token_request.seller_fee_basis_points
        )
