# src/services/wallet_service.py
import json
import os
from typing import Optional
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from ..utils.exceptions import WalletError
from ..utils.logger import get_logger

class WalletService:
    """Service for wallet operations"""
    
    def __init__(self, wallet_path: str):
        self.wallet_path = wallet_path
        self.logger = get_logger(__name__)
        self._keypair: Optional[Keypair] = None
    
    def load_keypair(self) -> Keypair:
        """Load keypair from wallet file"""
        if self._keypair:
            return self._keypair
            
        try:
            if not os.path.exists(self.wallet_path):
                raise WalletError(f"Wallet file not found at {self.wallet_path}", self.wallet_path)
            
            with open(self.wallet_path, 'r') as f:
                private_key_data = json.load(f)
            
            if isinstance(private_key_data, list) and all(isinstance(x, int) for x in private_key_data):
                self._keypair = Keypair.from_bytes(bytes(private_key_data))
            else:
                self._keypair = Keypair.from_base58_string(str(private_key_data))
            
            self.logger.info(f"Wallet loaded successfully: {self._keypair.pubkey()}")
            return self._keypair
            
        except FileNotFoundError:
            raise WalletError(f"Wallet file not found at {self.wallet_path}", self.wallet_path)
        except json.JSONDecodeError:
            raise WalletError(f"Invalid JSON format in wallet file: {self.wallet_path}", self.wallet_path)
        except Exception as e:
            raise WalletError(f"Error loading wallet: {str(e)}", self.wallet_path)
    
    def get_public_key(self) -> Pubkey:
        """Get wallet public key"""
        keypair = self.load_keypair()
        return keypair.pubkey()
    
    def create_new_wallet(self, save_path: Optional[str] = None) -> Keypair:
        """Create a new wallet and optionally save it"""
        new_keypair = Keypair()
        
        if save_path:
            self.save_keypair(new_keypair, save_path)
        
        self.logger.info(f"New wallet created: {new_keypair.pubkey()}")
        return new_keypair
    
    def save_keypair(self, keypair: Keypair, file_path: str) -> None:
        """Save keypair to file"""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # Convert keypair to bytes and then to list for JSON serialization
            private_key_list = list(keypair.secret())
            
            with open(file_path, 'w') as f:
                json.dump(private_key_list, f)
            
            self.logger.info(f"Wallet saved to: {file_path}")
            
        except Exception as e:
            raise WalletError(f"Error saving wallet: {str(e)}", file_path)
    
    def validate_wallet(self) -> bool:
        """Validate that wallet file exists and is readable"""
        try:
            self.load_keypair()
            return True
        except WalletError:
            return False
    
    def get_wallet_info(self) -> dict:
        """Get wallet information"""
        try:
            keypair = self.load_keypair()
            return {
                "public_key": str(keypair.pubkey()),
                "wallet_path": self.wallet_path,
                "is_valid": True
            }
        except WalletError as e:
            return {
                "public_key": None,
                "wallet_path": self.wallet_path,
                "is_valid": False,
                "error": str(e)
            }
