# src/utils/exceptions.py

class SolanaAPIError(Exception):
    """Base exception for Solana API errors"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)

class ValidationError(SolanaAPIError):
    """Exception for validation errors"""
    
    def __init__(self, message: str, field: str = None, value=None):
        self.field = field
        self.value = value
        super().__init__(message, "VALIDATION_ERROR", {"field": field, "value": value})

class WalletError(SolanaAPIError):
    """Exception for wallet-related errors"""
    
    def __init__(self, message: str, wallet_path: str = None):
        self.wallet_path = wallet_path
        super().__init__(message, "WALLET_ERROR", {"wallet_path": wallet_path})

class TransactionError(SolanaAPIError):
    """Exception for transaction errors"""
    
    def __init__(self, message: str, signature: str = None, logs: list = None):
        self.signature = signature
        self.logs = logs
        super().__init__(message, "TRANSACTION_ERROR", {"signature": signature, "logs": logs})

class NetworkError(SolanaAPIError):
    """Exception for network/RPC errors"""
    
    def __init__(self, message: str, rpc_url: str = None, status_code: int = None):
        self.rpc_url = rpc_url
        self.status_code = status_code
        super().__init__(message, "NETWORK_ERROR", {"rpc_url": rpc_url, "status_code": status_code})

class InsufficientFundsError(SolanaAPIError):
    """Exception for insufficient funds errors"""
    
    def __init__(self, message: str, required_amount: float = None, available_amount: float = None):
        self.required_amount = required_amount
        self.available_amount = available_amount
        super().__init__(
            message, 
            "INSUFFICIENT_FUNDS", 
            {"required": required_amount, "available": available_amount}
        )
