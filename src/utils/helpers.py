# src/utils/helpers.py
from typing import Optional

def generate_explorer_url(signature: str, network: str = 'devnet') -> str:
    """Generate Solana Explorer URL for transaction"""
    base_url = "https://explorer.solana.com/tx"
    
    if network == 'mainnet-beta':
        return f"{base_url}/{signature}"
    else:
        return f"{base_url}/{signature}?cluster={network}"

def format_lamports(lamports: int) -> float:
    """Convert lamports to SOL"""
    return lamports / 1_000_000_000

def format_sol_to_lamports(sol: float) -> int:
    """Convert SOL to lamports"""
    return int(sol * 1_000_000_000)

def truncate_address(address: str, start_chars: int = 4, end_chars: int = 4) -> str:
    """Truncate Solana address for display"""
    if len(address) <= start_chars + end_chars:
        return address
    return f"{address[:start_chars]}...{address[-end_chars:]}"

def validate_network(network: str) -> bool:
    """Validate Solana network name"""
    valid_networks = ['devnet', 'testnet', 'mainnet-beta']
    return network in valid_networks

def get_rpc_url(network: str) -> str:
    """Get RPC URL for network"""
    urls = {
        'devnet': 'https://api.devnet.solana.com',
        'testnet': 'https://api.testnet.solana.com',
        'mainnet-beta': 'https://api.mainnet-beta.solana.com'
    }
    return urls.get(network, urls['devnet'])

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file operations"""
    import re
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove leading/trailing spaces and dots
    sanitized = sanitized.strip(' .')
    # Limit length
    return sanitized[:255] if len(sanitized) > 255 else sanitized
