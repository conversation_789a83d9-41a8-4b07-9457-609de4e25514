# src/utils/validators.py
import re
from typing import List
from solders.pubkey import Pubkey
from ..models.token import TokenRequest
from .exceptions import ValidationError

def validate_solana_address(address: str) -> bool:
    """Validate Solana public key address"""
    try:
        Pubkey.from_string(address)
        return True
    except Exception:
        return False

def validate_token_request(request_data: dict) -> TokenRequest:
    """Validate and create TokenRequest from request data"""
    
    # Extract and validate required fields
    name = request_data.get('name', '').strip()
    symbol = request_data.get('symbol', '').strip()
    amount = request_data.get('amount', 1)
    
    # Create TokenRequest object
    token_request = TokenRequest(
        name=name,
        symbol=symbol,
        amount=amount,
        metadata_uri=request_data.get('metadata_uri'),
        description=request_data.get('description'),
        image_uri=request_data.get('image_uri'),
        attributes=request_data.get('attributes'),
        seller_fee_basis_points=request_data.get('seller_fee_basis_points', 0),
        is_mutable=request_data.get('is_mutable', True)
    )
    
    # Validate the request
    errors = token_request.validate()
    if errors:
        raise ValidationError(f"Validation failed: {'; '.join(errors)}")
    
    return token_request

def validate_symbol(symbol: str) -> bool:
    """Validate token symbol format"""
    if not symbol:
        return False
    
    # Symbol should be alphanumeric, uppercase, 1-10 characters
    pattern = r'^[A-Z0-9]{1,10}$'
    return bool(re.match(pattern, symbol))

def validate_name(name: str) -> bool:
    """Validate token name format"""
    if not name:
        return False
    
    # Name should be 1-32 characters, allow letters, numbers, spaces, and basic punctuation
    if len(name) > 32:
        return False
    
    # Check for valid characters
    pattern = r'^[a-zA-Z0-9\s\-_\.]+$'
    return bool(re.match(pattern, name))

def validate_metadata_uri(uri: str) -> bool:
    """Validate metadata URI format"""
    if not uri:
        return True  # Optional field
    
    # Basic URL validation
    url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
    return bool(re.match(url_pattern, uri))

def validate_amount(amount: int) -> bool:
    """Validate token amount"""
    return isinstance(amount, int) and 1 <= amount <= 1000000

def validate_seller_fee(fee: int) -> bool:
    """Validate seller fee basis points (0-10000 = 0-100%)"""
    return isinstance(fee, int) and 0 <= fee <= 10000
