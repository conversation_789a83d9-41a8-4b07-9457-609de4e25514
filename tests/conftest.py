# tests/conftest.py
import pytest
import os
import tempfile
from src.app import create_app
from src.config import TestingConfig

@pytest.fixture
def app():
    """Create application for testing"""
    app = create_app('testing')
    
    # Create temporary wallet file for testing
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        # Mock wallet data (this is just for testing, not a real wallet)
        mock_wallet = [1, 2, 3, 4, 5] * 12 + [1, 2, 3, 4]  # 64 bytes
        import json
        json.dump(mock_wallet, f)
        app.config['SOLANA_WALLET_FILE_PATH'] = f.name
    
    yield app
    
    # Cleanup
    if os.path.exists(app.config['SOLANA_WALLET_FILE_PATH']):
        os.unlink(app.config['SOLANA_WALLET_FILE_PATH'])

@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()

@pytest.fixture
def runner(app):
    """Create test CLI runner"""
    return app.test_cli_runner()
