# tests/test_health.py
import pytest
import json

def test_health_endpoint(client):
    """Test health check endpoint"""
    response = client.get('/health')
    assert response.status_code == 200
    
    data = json.loads(response.data)
    assert data['status'] == 'healthy'
    assert 'message' in data
    assert 'version' in data

def test_status_endpoint(client):
    """Test status endpoint"""
    response = client.get('/status')
    # This might fail due to wallet/network issues in testing
    # but we can check that the endpoint exists
    assert response.status_code in [200, 500]  # Either works or fails gracefully
