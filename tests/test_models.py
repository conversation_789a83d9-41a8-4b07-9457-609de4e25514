# tests/test_models.py
import pytest
from src.models.token import TokenRequest, TokenResponse, TokenMetadata
from src.models.transaction import TransactionResult, TransactionStatus

def test_token_request_validation():
    """Test TokenRequest validation"""
    # Valid request
    valid_request = TokenRequest(
        name="Test Token",
        symbol="TEST",
        amount=1
    )
    errors = valid_request.validate()
    assert len(errors) == 0
    
    # Invalid name
    invalid_request = TokenRequest(
        name="",
        symbol="TEST",
        amount=1
    )
    errors = invalid_request.validate()
    assert len(errors) > 0
    assert any("name" in error.lower() for error in errors)
    
    # Invalid amount
    invalid_amount = TokenRequest(
        name="Test",
        symbol="TEST",
        amount=0
    )
    errors = invalid_amount.validate()
    assert len(errors) > 0
    assert any("amount" in error.lower() for error in errors)

def test_token_metadata():
    """Test TokenMetadata model"""
    metadata = TokenMetadata(
        name="Test NFT",
        symbol="TNFT",
        description="Test description"
    )
    
    data = metadata.to_dict()
    assert data['name'] == "Test NFT"
    assert data['symbol'] == "TNFT"
    assert data['description'] == "Test description"

def test_token_response():
    """Test TokenResponse model"""
    response = TokenResponse(
        success=True,
        message="Success",
        token_mint_address="test_address"
    )
    
    data = response.to_dict()
    assert data['success'] is True
    assert data['message'] == "Success"
    assert data['token_mint_address'] == "test_address"

def test_transaction_result():
    """Test TransactionResult model"""
    result = TransactionResult(
        signature="test_signature",
        status=TransactionStatus.CONFIRMED
    )
    
    assert result.is_successful is True
    assert result.is_failed is False
    
    data = result.to_dict()
    assert data['signature'] == "test_signature"
    assert data['status'] == "confirmed"
